"""
文本处理工具
"""
import re
import logging
from typing import List, Dict, Any, Set
from pathlib import Path


class TextProcessor:
    """文本处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 常见的编程语言关键词
        self.code_keywords = {
            'python': ['def', 'class', 'import', 'from', 'if', 'else', 'for', 'while', 'try', 'except'],
            'javascript': ['function', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'try', 'catch'],
            'java': ['public', 'private', 'class', 'interface', 'if', 'else', 'for', 'while', 'try', 'catch'],
            'cpp': ['class', 'struct', 'namespace', 'if', 'else', 'for', 'while', 'try', 'catch']
        }
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """从文本中提取关键词"""
        # 移除特殊字符，保留字母数字和中文
        cleaned_text = re.sub(r'[^\w\u4e00-\u9fff\s]', ' ', text)
        
        # 分词
        words = cleaned_text.lower().split()
        
        # 过滤短词和常见停用词
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        filtered_words = [word for word in words if len(word) > 2 and word not in stop_words]
        
        # 统计词频
        word_count = {}
        for word in filtered_words:
            word_count[word] = word_count.get(word, 0) + 1
        
        # 按频率排序并返回前N个
        sorted_words = sorted(word_count.items(), key=lambda x: x[1], reverse=True)
        return [word for word, count in sorted_words[:max_keywords]]
    
    def detect_language(self, file_path: str) -> str:
        """检测文件的编程语言"""
        file_ext = Path(file_path).suffix.lower()
        
        ext_mapping = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.md': 'markdown',
            '.html': 'html',
            '.css': 'css',
            '.sql': 'sql',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.json': 'json',
            '.xml': 'xml'
        }
        
        return ext_mapping.get(file_ext, 'text')
    
    def extract_code_structure(self, content: str, language: str) -> Dict[str, Any]:
        """提取代码结构信息"""
        structure = {
            'functions': [],
            'classes': [],
            'imports': [],
            'comments': [],
            'language': language
        }
        
        lines = content.split('\n')
        
        if language == 'python':
            structure.update(self._extract_python_structure(lines))
        elif language in ['javascript', 'typescript']:
            structure.update(self._extract_js_structure(lines))
        elif language == 'java':
            structure.update(self._extract_java_structure(lines))
        
        return structure
    
    def _extract_python_structure(self, lines: List[str]) -> Dict[str, List[str]]:
        """提取Python代码结构"""
        functions = []
        classes = []
        imports = []
        comments = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # 函数定义
            if stripped.startswith('def '):
                func_match = re.match(r'def\s+(\w+)', stripped)
                if func_match:
                    functions.append(f"{func_match.group(1)} (line {i+1})")
            
            # 类定义
            elif stripped.startswith('class '):
                class_match = re.match(r'class\s+(\w+)', stripped)
                if class_match:
                    classes.append(f"{class_match.group(1)} (line {i+1})")
            
            # 导入语句
            elif stripped.startswith(('import ', 'from ')):
                imports.append(stripped)
            
            # 注释
            elif stripped.startswith('#'):
                comments.append(stripped)
        
        return {
            'functions': functions,
            'classes': classes,
            'imports': imports,
            'comments': comments
        }
    
    def _extract_js_structure(self, lines: List[str]) -> Dict[str, List[str]]:
        """提取JavaScript/TypeScript代码结构"""
        functions = []
        classes = []
        imports = []
        comments = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # 函数定义
            func_patterns = [
                r'function\s+(\w+)',
                r'const\s+(\w+)\s*=\s*\(',
                r'let\s+(\w+)\s*=\s*\(',
                r'(\w+)\s*:\s*function',
                r'(\w+)\s*=>\s*'
            ]
            
            for pattern in func_patterns:
                match = re.search(pattern, stripped)
                if match:
                    functions.append(f"{match.group(1)} (line {i+1})")
                    break
            
            # 类定义
            if stripped.startswith('class '):
                class_match = re.match(r'class\s+(\w+)', stripped)
                if class_match:
                    classes.append(f"{class_match.group(1)} (line {i+1})")
            
            # 导入语句
            elif stripped.startswith(('import ', 'const ', 'require(')):
                if 'import' in stripped or 'require' in stripped:
                    imports.append(stripped)
            
            # 注释
            elif stripped.startswith('//') or stripped.startswith('/*'):
                comments.append(stripped)
        
        return {
            'functions': functions,
            'classes': classes,
            'imports': imports,
            'comments': comments
        }
    
    def _extract_java_structure(self, lines: List[str]) -> Dict[str, List[str]]:
        """提取Java代码结构"""
        functions = []
        classes = []
        imports = []
        comments = []
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # 方法定义
            if re.search(r'(public|private|protected).*\s+\w+\s*\(', stripped):
                method_match = re.search(r'\s+(\w+)\s*\(', stripped)
                if method_match:
                    functions.append(f"{method_match.group(1)} (line {i+1})")
            
            # 类定义
            elif stripped.startswith(('public class', 'class', 'interface')):
                class_match = re.search(r'(class|interface)\s+(\w+)', stripped)
                if class_match:
                    classes.append(f"{class_match.group(2)} (line {i+1})")
            
            # 导入语句
            elif stripped.startswith('import '):
                imports.append(stripped)
            
            # 注释
            elif stripped.startswith('//') or stripped.startswith('/*'):
                comments.append(stripped)
        
        return {
            'functions': functions,
            'classes': classes,
            'imports': imports,
            'comments': comments
        }
    
    def summarize_content(self, content: str, max_length: int = 200) -> str:
        """生成内容摘要"""
        if len(content) <= max_length:
            return content
        
        # 按句子分割
        sentences = re.split(r'[.!?。！？]', content)
        
        summary = ""
        for sentence in sentences:
            if len(summary + sentence) <= max_length:
                summary += sentence + "。"
            else:
                break
        
        if not summary:
            summary = content[:max_length] + "..."
        
        return summary.strip()
    
    def generate_auto_tags(self, content: str, file_path: str = "") -> List[str]:
        """自动生成标签"""
        tags = set()
        
        # 基于文件路径的标签
        if file_path:
            path_parts = Path(file_path).parts
            for part in path_parts:
                if part not in ['.', '..', '']:
                    tags.add(part.lower())
            
            # 文件扩展名标签
            language = self.detect_language(file_path)
            if language != 'text':
                tags.add(language)
        
        # 基于内容的标签
        keywords = self.extract_keywords(content, 5)
        tags.update(keywords)
        
        # 编程相关标签
        for lang, keywords_list in self.code_keywords.items():
            if any(keyword in content.lower() for keyword in keywords_list):
                tags.add(lang)
        
        return list(tags)[:10]  # 限制标签数量
