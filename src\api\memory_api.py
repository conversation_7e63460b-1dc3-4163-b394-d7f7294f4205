"""
记忆管理API接口
"""
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from http.server import <PERSON>TTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

from ..core.memory_manager import MemoryManager


class MemoryAPIHandler(BaseHTTPRequestHandler):
    """记忆API请求处理器"""
    
    def __init__(self, *args, memory_manager: MemoryManager = None, **kwargs):
        self.memory_manager = memory_manager
        self.logger = logging.getLogger(__name__)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            if path == '/api/memories':
                self._handle_get_memories(query_params)
            elif path.startswith('/api/memories/'):
                memory_id = path.split('/')[-1]
                self._handle_get_memory(memory_id)
            elif path == '/api/statistics':
                self._handle_get_statistics()
            elif path == '/api/context':
                self._handle_get_context()
            elif path == '/api/health':
                self._handle_health_check()
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"GET请求处理失败: {e}")
            self._send_error(500, str(e))
    
    def do_POST(self):
        """处理POST请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8')) if post_data else {}
            
            if path == '/api/memories':
                self._handle_create_memory(data)
            elif path == '/api/memories/search':
                self._handle_search_memories(data)
            elif path == '/api/conversations':
                self._handle_create_conversation(data)
            elif path == '/api/tasks':
                self._handle_create_task(data)
            elif path == '/api/analyze':
                self._handle_analyze_file(data)
            elif path == '/api/export':
                self._handle_export_memories(data)
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"POST请求处理失败: {e}")
            self._send_error(500, str(e))
    
    def do_PUT(self):
        """处理PUT请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path.startswith('/api/memories/'):
                memory_id = path.split('/')[-1]
                content_length = int(self.headers.get('Content-Length', 0))
                put_data = self.rfile.read(content_length)
                data = json.loads(put_data.decode('utf-8'))
                self._handle_update_memory(memory_id, data)
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"PUT请求处理失败: {e}")
            self._send_error(500, str(e))
    
    def do_DELETE(self):
        """处理DELETE请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path.startswith('/api/memories/'):
                memory_id = path.split('/')[-1]
                self._handle_delete_memory(memory_id)
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            self.logger.error(f"DELETE请求处理失败: {e}")
            self._send_error(500, str(e))
    
    def _handle_get_memories(self, query_params: Dict[str, List[str]]):
        """获取记忆列表"""
        # 解析查询参数
        query = query_params.get('q', [''])[0]
        memory_types = query_params.get('type', [])
        tags = query_params.get('tags', [])
        limit = int(query_params.get('limit', ['100'])[0])
        
        # 日期范围
        date_from = None
        date_to = None
        if 'date_from' in query_params:
            date_from = datetime.fromisoformat(query_params['date_from'][0])
        if 'date_to' in query_params:
            date_to = datetime.fromisoformat(query_params['date_to'][0])
        
        memories = self.memory_manager.search_memories(
            query=query,
            memory_types=memory_types,
            tags=tags,
            date_from=date_from,
            date_to=date_to,
            limit=limit
        )
        
        response_data = {
            'memories': [memory.to_dict() for memory in memories],
            'total': len(memories)
        }
        
        self._send_json_response(response_data)
    
    def _handle_get_memory(self, memory_id: str):
        """获取单个记忆"""
        memory = self.memory_manager.get_memory(memory_id)
        if memory:
            self._send_json_response(memory.to_dict())
        else:
            self._send_error(404, "Memory not found")
    
    def _handle_get_statistics(self):
        """获取统计信息"""
        stats = self.memory_manager.get_statistics()
        self._send_json_response(stats)
    
    def _handle_get_context(self):
        """获取项目上下文"""
        context = self.memory_manager.get_project_context()
        if context:
            self._send_json_response(context.to_dict())
        else:
            self._send_error(404, "Project context not found")
    
    def _handle_health_check(self):
        """健康检查"""
        health_data = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0'
        }
        self._send_json_response(health_data)
    
    def _handle_create_memory(self, data: Dict[str, Any]):
        """创建记忆"""
        memory_type = data.get('memory_type', '')
        content = data.get('content', '')
        context = data.get('context', {})
        tags = data.get('tags', [])
        
        if not memory_type or not content:
            self._send_error(400, "memory_type and content are required")
            return
        
        memory_id = self.memory_manager.add_memory(memory_type, content, context, tags)
        
        response_data = {
            'id': memory_id,
            'message': 'Memory created successfully'
        }
        self._send_json_response(response_data, status_code=201)
    
    def _handle_search_memories(self, data: Dict[str, Any]):
        """搜索记忆"""
        query = data.get('query', '')
        memory_types = data.get('memory_types', [])
        tags = data.get('tags', [])
        limit = data.get('limit', 100)
        
        # 日期范围
        date_from = None
        date_to = None
        if 'date_from' in data:
            date_from = datetime.fromisoformat(data['date_from'])
        if 'date_to' in data:
            date_to = datetime.fromisoformat(data['date_to'])
        
        memories = self.memory_manager.search_memories(
            query=query,
            memory_types=memory_types,
            tags=tags,
            date_from=date_from,
            date_to=date_to,
            limit=limit
        )
        
        response_data = {
            'memories': [memory.to_dict() for memory in memories],
            'total': len(memories),
            'query': data
        }
        
        self._send_json_response(response_data)
    
    def _handle_create_conversation(self, data: Dict[str, Any]):
        """创建对话记忆"""
        content = data.get('content', '')
        participant = data.get('participant', 'user')
        topic = data.get('topic', '')
        
        if not content:
            self._send_error(400, "content is required")
            return
        
        memory_id = self.memory_manager.add_conversation_memory(content, participant, topic)
        
        response_data = {
            'id': memory_id,
            'message': 'Conversation memory created successfully'
        }
        self._send_json_response(response_data, status_code=201)
    
    def _handle_create_task(self, data: Dict[str, Any]):
        """创建任务记忆"""
        task_name = data.get('name', '')
        description = data.get('description', '')
        state = data.get('state', 'created')
        
        if not task_name:
            self._send_error(400, "name is required")
            return
        
        memory_id = self.memory_manager.add_task_memory(task_name, description, state)
        
        response_data = {
            'id': memory_id,
            'message': 'Task memory created successfully'
        }
        self._send_json_response(response_data, status_code=201)

    def _handle_analyze_file(self, data: Dict[str, Any]):
        """分析文件"""
        file_path = data.get('file_path', '')

        if not file_path:
            self._send_error(400, "file_path is required")
            return

        memory = self.memory_manager.analyze_file(file_path)

        if memory:
            response_data = {
                'id': memory.id,
                'memory': memory.to_dict(),
                'message': 'File analyzed successfully'
            }
            self._send_json_response(response_data, status_code=201)
        else:
            self._send_error(400, "Failed to analyze file")

    def _handle_export_memories(self, data: Dict[str, Any]):
        """导出记忆"""
        output_path = data.get('output_path', 'memories_export.json')
        memory_types = data.get('memory_types', [])

        success = self.memory_manager.export_memories(output_path, memory_types)

        if success:
            response_data = {
                'message': 'Memories exported successfully',
                'output_path': output_path
            }
            self._send_json_response(response_data)
        else:
            self._send_error(500, "Failed to export memories")

    def _handle_update_memory(self, memory_id: str, data: Dict[str, Any]):
        """更新记忆"""
        memory = self.memory_manager.get_memory(memory_id)
        if not memory:
            self._send_error(404, "Memory not found")
            return

        # 更新字段
        if 'content' in data:
            memory.content = data['content']
        if 'context' in data:
            memory.context = data['context']
        if 'tags' in data:
            memory.tags = data['tags']
        if 'memory_type' in data:
            memory.memory_type = data['memory_type']

        # 重新生成哈希值
        memory.content_hash = memory._generate_hash()

        success = self.memory_manager.update_memory(memory)

        if success:
            response_data = {
                'message': 'Memory updated successfully',
                'memory': memory.to_dict()
            }
            self._send_json_response(response_data)
        else:
            self._send_error(500, "Failed to update memory")

    def _handle_delete_memory(self, memory_id: str):
        """删除记忆"""
        success = self.memory_manager.delete_memory(memory_id)

        if success:
            response_data = {
                'message': 'Memory deleted successfully'
            }
            self._send_json_response(response_data)
        else:
            self._send_error(404, "Memory not found")

    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))

    def _send_error(self, status_code: int, message: str):
        """发送错误响应"""
        error_data = {
            'error': message,
            'status_code': status_code,
            'timestamp': datetime.now().isoformat()
        }
        self._send_json_response(error_data, status_code)

    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()


class MemoryAPIServer:
    """记忆API服务器"""

    def __init__(self, memory_manager: MemoryManager, host: str = 'localhost', port: int = 8080):
        self.memory_manager = memory_manager
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.logger = logging.getLogger(__name__)

    def start(self):
        """启动API服务器"""
        def handler(*args, **kwargs):
            return MemoryAPIHandler(*args, memory_manager=self.memory_manager, **kwargs)

        self.server = HTTPServer((self.host, self.port), handler)
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()

        self.logger.info(f"记忆API服务器启动: http://{self.host}:{self.port}")

    def stop(self):
        """停止API服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.logger.info("记忆API服务器已停止")

    def is_running(self) -> bool:
        """检查服务器是否运行中"""
        return self.server_thread and self.server_thread.is_alive()
