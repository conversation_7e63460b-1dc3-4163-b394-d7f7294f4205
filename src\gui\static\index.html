<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoMem 记忆管理系统</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-brain"></i> AutoMem</h1>
                <p>记忆增强系统</p>
            </div>
            <nav class="nav">
                <button class="nav-btn active" onclick="showDashboard()">
                    <i class="fas fa-tachometer-alt"></i> 仪表板
                </button>
                <button class="nav-btn" onclick="showMemories()">
                    <i class="fas fa-list"></i> 记忆列表
                </button>
                <button class="nav-btn" onclick="showSearch()">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <button class="nav-btn" onclick="showCreate()">
                    <i class="fas fa-plus"></i> 创建记忆
                </button>
            </nav>
        </header>
        
        <main id="content" class="main-content">
            <!-- 仪表板 -->
            <div id="dashboard" class="page active">
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-memories">-</h3>
                            <p>总记忆数</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="code-memories">-</h3>
                            <p>代码记忆</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="conversation-memories">-</h3>
                            <p>对话记忆</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="task-memories">-</h3>
                            <p>任务记忆</p>
                        </div>
                    </div>
                </div>
                
                <div class="recent-section">
                    <h2><i class="fas fa-clock"></i> 最近记忆</h2>
                    <div id="recent-memories" class="memory-list">
                        <!-- 最近记忆将在这里显示 -->
                    </div>
                </div>
            </div>
            
            <!-- 记忆列表 -->
            <div id="memories" class="page">
                <div class="page-header">
                    <h2><i class="fas fa-list"></i> 记忆列表</h2>
                    <div class="filters">
                        <select id="type-filter">
                            <option value="">所有类型</option>
                            <option value="code">代码</option>
                            <option value="conversation">对话</option>
                            <option value="task">任务</option>
                            <option value="file_change">文件变更</option>
                        </select>
                        <button onclick="loadMemories()" class="btn btn-primary">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>
                </div>
                
                <div id="memories-list" class="memory-list">
                    <!-- 记忆列表将在这里显示 -->
                </div>
                
                <div id="pagination" class="pagination">
                    <!-- 分页控件将在这里显示 -->
                </div>
            </div>
            
            <!-- 搜索页面 -->
            <div id="search" class="page">
                <div class="page-header">
                    <h2><i class="fas fa-search"></i> 搜索记忆</h2>
                </div>
                
                <div class="search-form">
                    <div class="form-group">
                        <label for="search-query">搜索关键词</label>
                        <input type="text" id="search-query" placeholder="输入搜索关键词...">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="search-type">记忆类型</label>
                            <select id="search-type" multiple>
                                <option value="code">代码</option>
                                <option value="conversation">对话</option>
                                <option value="task">任务</option>
                                <option value="file_change">文件变更</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="search-tags">标签</label>
                            <input type="text" id="search-tags" placeholder="标签1,标签2...">
                        </div>
                    </div>
                    
                    <button onclick="performSearch()" class="btn btn-primary">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                
                <div id="search-results" class="memory-list">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </div>
            
            <!-- 创建记忆页面 -->
            <div id="create" class="page">
                <div class="page-header">
                    <h2><i class="fas fa-plus"></i> 创建记忆</h2>
                </div>
                
                <form class="create-form" onsubmit="createMemory(event)">
                    <div class="form-group">
                        <label for="create-type">记忆类型</label>
                        <select id="create-type" required>
                            <option value="">选择类型</option>
                            <option value="code">代码</option>
                            <option value="conversation">对话</option>
                            <option value="task">任务</option>
                            <option value="note">笔记</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="create-content">内容</label>
                        <textarea id="create-content" rows="6" placeholder="输入记忆内容..." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="create-tags">标签</label>
                        <input type="text" id="create-tags" placeholder="标签1,标签2...">
                    </div>
                    
                    <div class="form-group">
                        <label for="create-context">上下文 (JSON)</label>
                        <textarea id="create-context" rows="4" placeholder='{"key": "value"}'></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> 创建记忆
                    </button>
                </form>
            </div>
        </main>
        
        <!-- 记忆详情模态框 -->
        <div id="memory-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">记忆详情</h3>
                    <span class="close" onclick="closeModal()">&times;</span>
                </div>
                <div class="modal-body" id="modal-body">
                    <!-- 记忆详情将在这里显示 -->
                </div>
            </div>
        </div>
        
        <!-- 加载指示器 -->
        <div id="loading" class="loading hidden">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
        
        <!-- 消息提示 -->
        <div id="message" class="message hidden">
            <span id="message-text"></span>
            <button onclick="hideMessage()">&times;</button>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
