"""
上下文收集器 - 自动收集项目上下文信息
"""
import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import json

from ..storage.models import MemoryEntry, ProjectContext
from ..utils.file_monitor import FileMonitor
from ..utils.text_processor import TextProcessor


class ContextCollector:
    """上下文收集器"""
    
    def __init__(self, project_root: str, config: Dict[str, Any] = None):
        self.project_root = Path(project_root).resolve()
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.text_processor = TextProcessor()
        self.file_monitor = FileMonitor(
            str(self.project_root),
            self.config.get('watch_patterns', ['*.py', '*.js', '*.md']),
            self.config.get('ignore_patterns', ['__pycache__', '.git', 'node_modules'])
        )
        
        # 回调函数列表
        self.memory_callbacks: List[Callable[[MemoryEntry], None]] = []
        self.context_callbacks: List[Callable[[ProjectContext], None]] = []
        
        # 设置文件监控回调
        self.file_monitor.add_change_callback(self._on_file_change)
    
    def add_memory_callback(self, callback: Callable[[MemoryEntry], None]):
        """添加记忆回调函数"""
        self.memory_callbacks.append(callback)
    
    def add_context_callback(self, callback: Callable[[ProjectContext], None]):
        """添加上下文回调函数"""
        self.context_callbacks.append(callback)
    
    def start_monitoring(self):
        """开始监控"""
        self.file_monitor.start_monitoring()
        self.logger.info("上下文收集器开始监控")
    
    def stop_monitoring(self):
        """停止监控"""
        self.file_monitor.stop_monitoring()
        self.logger.info("上下文收集器停止监控")
    
    def collect_initial_context(self) -> ProjectContext:
        """收集初始项目上下文"""
        self.logger.info("开始收集初始项目上下文")
        
        # 获取文件结构
        file_structure = self.file_monitor.get_file_structure()
        
        # 分析依赖关系
        dependencies = self._analyze_dependencies()
        
        # 创建项目上下文
        context = ProjectContext(
            project_path=str(self.project_root),
            file_structure=file_structure,
            dependencies=dependencies,
            last_updated=datetime.now(),
            metadata={
                'total_files': self._count_files(file_structure),
                'languages': self._detect_languages(),
                'project_type': self._detect_project_type()
            }
        )
        
        # 触发回调
        for callback in self.context_callbacks:
            try:
                callback(context)
            except Exception as e:
                self.logger.error(f"上下文回调执行失败: {e}")
        
        return context
    
    def collect_file_memory(self, file_path: str, change_type: str = "analysis") -> Optional[MemoryEntry]:
        """收集文件相关的记忆"""
        try:
            file_path_obj = Path(file_path)
            
            # 检查文件是否存在
            if not file_path_obj.exists():
                if change_type == "deleted":
                    return self._create_deletion_memory(file_path)
                return None
            
            # 读取文件内容
            try:
                with open(file_path_obj, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 尝试其他编码
                try:
                    with open(file_path_obj, 'r', encoding='gbk') as f:
                        content = f.read()
                except:
                    self.logger.warning(f"无法读取文件: {file_path}")
                    return None
            
            # 限制内容长度
            max_length = self.config.get('max_content_length', 5000)
            if len(content) > max_length:
                content = content[:max_length] + "..."
            
            # 分析文件
            language = self.text_processor.detect_language(file_path)
            code_structure = self.text_processor.extract_code_structure(content, language)
            auto_tags = self.text_processor.generate_auto_tags(content, file_path)
            
            # 创建记忆条目
            memory = MemoryEntry(
                memory_type="file_change" if change_type != "analysis" else "code",
                content=self.text_processor.summarize_content(content),
                context={
                    'file_path': str(file_path_obj.relative_to(self.project_root)),
                    'change_type': change_type,
                    'language': language,
                    'structure': code_structure,
                    'file_size': file_path_obj.stat().st_size,
                    'modified_time': file_path_obj.stat().st_mtime
                },
                tags=auto_tags,
                project_path=str(self.project_root)
            )
            
            # 触发回调
            for callback in self.memory_callbacks:
                try:
                    callback(memory)
                except Exception as e:
                    self.logger.error(f"记忆回调执行失败: {e}")
            
            return memory
            
        except Exception as e:
            self.logger.error(f"收集文件记忆失败 {file_path}: {e}")
            return None
    
    def collect_conversation_memory(self, conversation_data: Dict[str, Any]) -> MemoryEntry:
        """收集对话记忆"""
        content = conversation_data.get('content', '')
        
        # 提取关键信息
        keywords = self.text_processor.extract_keywords(content)
        auto_tags = ['conversation'] + keywords[:5]
        
        memory = MemoryEntry(
            memory_type="conversation",
            content=self.text_processor.summarize_content(content),
            context={
                'timestamp': conversation_data.get('timestamp', datetime.now().isoformat()),
                'participant': conversation_data.get('participant', 'user'),
                'topic': conversation_data.get('topic', ''),
                'full_content': content if len(content) <= 1000 else content[:1000] + "..."
            },
            tags=auto_tags,
            project_path=str(self.project_root)
        )
        
        # 触发回调
        for callback in self.memory_callbacks:
            try:
                callback(memory)
            except Exception as e:
                self.logger.error(f"对话记忆回调执行失败: {e}")
        
        return memory
    
    def collect_task_memory(self, task_data: Dict[str, Any]) -> MemoryEntry:
        """收集任务记忆"""
        content = f"任务: {task_data.get('name', '')} - {task_data.get('description', '')}"
        
        memory = MemoryEntry(
            memory_type="task",
            content=content,
            context={
                'task_id': task_data.get('id', ''),
                'name': task_data.get('name', ''),
                'description': task_data.get('description', ''),
                'state': task_data.get('state', ''),
                'created_time': task_data.get('created_time', datetime.now().isoformat()),
                'updated_time': task_data.get('updated_time', datetime.now().isoformat())
            },
            tags=['task', task_data.get('state', '').lower()],
            project_path=str(self.project_root)
        )
        
        # 触发回调
        for callback in self.memory_callbacks:
            try:
                callback(memory)
            except Exception as e:
                self.logger.error(f"任务记忆回调执行失败: {e}")
        
        return memory
    
    def _on_file_change(self, file_path: str, change_type: str, dest_path: str = ""):
        """文件变更回调"""
        self.logger.info(f"检测到文件变更: {change_type} - {file_path}")
        
        # 收集文件变更记忆
        memory = self.collect_file_memory(file_path, change_type)
        
        if memory:
            self.logger.info(f"已收集文件变更记忆: {memory.id}")
    
    def _create_deletion_memory(self, file_path: str) -> MemoryEntry:
        """创建文件删除记忆"""
        return MemoryEntry(
            memory_type="file_change",
            content=f"文件已删除: {file_path}",
            context={
                'file_path': file_path,
                'change_type': 'deleted',
                'deletion_time': datetime.now().isoformat()
            },
            tags=['deleted', 'file_change'],
            project_path=str(self.project_root)
        )

    def _analyze_dependencies(self) -> List[str]:
        """分析项目依赖"""
        dependencies = []

        # Python项目
        requirements_files = ['requirements.txt', 'Pipfile', 'pyproject.toml']
        for req_file in requirements_files:
            req_path = self.project_root / req_file
            if req_path.exists():
                dependencies.extend(self._parse_python_dependencies(req_path))

        # Node.js项目
        package_json = self.project_root / 'package.json'
        if package_json.exists():
            dependencies.extend(self._parse_nodejs_dependencies(package_json))

        # Java项目
        pom_xml = self.project_root / 'pom.xml'
        if pom_xml.exists():
            dependencies.append('maven-project')

        build_gradle = self.project_root / 'build.gradle'
        if build_gradle.exists():
            dependencies.append('gradle-project')

        return list(set(dependencies))

    def _parse_python_dependencies(self, file_path: Path) -> List[str]:
        """解析Python依赖"""
        deps = []
        try:
            if file_path.name == 'requirements.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # 提取包名
                            pkg_name = line.split('==')[0].split('>=')[0].split('<=')[0].split('>')[0].split('<')[0]
                            deps.append(pkg_name.strip())
            elif file_path.name == 'pyproject.toml':
                deps.append('poetry-project')
            elif file_path.name == 'Pipfile':
                deps.append('pipenv-project')
        except Exception as e:
            self.logger.warning(f"解析Python依赖失败 {file_path}: {e}")

        return deps

    def _parse_nodejs_dependencies(self, file_path: Path) -> List[str]:
        """解析Node.js依赖"""
        deps = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)

                # 添加依赖包
                for dep_type in ['dependencies', 'devDependencies']:
                    if dep_type in package_data:
                        deps.extend(package_data[dep_type].keys())

                # 添加框架信息
                if 'react' in deps:
                    deps.append('react-project')
                if 'vue' in deps:
                    deps.append('vue-project')
                if 'angular' in deps:
                    deps.append('angular-project')
                if 'express' in deps:
                    deps.append('express-project')

        except Exception as e:
            self.logger.warning(f"解析Node.js依赖失败 {file_path}: {e}")

        return deps

    def _count_files(self, structure: Dict[str, Any]) -> int:
        """递归计算文件数量"""
        count = 0
        if structure.get('type') == 'file':
            return 1

        children = structure.get('children', {})
        for child in children.values():
            count += self._count_files(child)

        return count

    def _detect_languages(self) -> List[str]:
        """检测项目中使用的编程语言"""
        languages = set()

        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                language = self.text_processor.detect_language(str(file_path))
                if language != 'text':
                    languages.add(language)

        return list(languages)

    def _detect_project_type(self) -> str:
        """检测项目类型"""
        # 检查特征文件
        if (self.project_root / 'package.json').exists():
            return 'nodejs'
        elif (self.project_root / 'requirements.txt').exists() or (self.project_root / 'setup.py').exists():
            return 'python'
        elif (self.project_root / 'pom.xml').exists():
            return 'java-maven'
        elif (self.project_root / 'build.gradle').exists():
            return 'java-gradle'
        elif (self.project_root / 'Cargo.toml').exists():
            return 'rust'
        elif (self.project_root / 'go.mod').exists():
            return 'go'
        elif (self.project_root / '.csproj').exists():
            return 'dotnet'
        else:
            return 'unknown'
