# AutoMem 使用指南

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 初始化系统

```bash
python src/main.py init
```

### 3. 启动系统

```bash
python src/main.py
```

系统将自动：
- 扫描项目文件结构
- 开始监控文件变更
- 启动API服务器（默认端口8080）

## 命令行使用

### 基本命令

```bash
# 初始化系统
python src/main.py init

# 启动完整系统（监控 + API）
python src/main.py

# 仅启动API服务器
python src/main.py --api-only

# 仅启动监控（不启动API）
python src/main.py --no-api

# 指定配置文件
python src/main.py -c custom_config.yaml

# 指定项目根目录
python src/main.py -p /path/to/project
```

### 分析命令

```bash
# 分析单个文件
python src/main.py analyze src/main.py

# 搜索记忆
python src/main.py search "函数定义"
python src/main.py search "用户认证" --type code --limit 5

# 查看统计信息
python src/main.py stats

# 导出记忆数据
python src/main.py export memories.json
python src/main.py export code_memories.json --type code

# 清理过期记忆
python src/main.py cleanup --days 30
```

## API使用

### 基础API

#### 获取记忆列表
```bash
curl "http://localhost:8080/api/memories?q=函数&limit=10"
```

#### 创建记忆
```bash
curl -X POST http://localhost:8080/api/memories \
  -H "Content-Type: application/json" \
  -d '{
    "memory_type": "code",
    "content": "实现了用户登录功能",
    "context": {"file": "auth.py", "function": "login"},
    "tags": ["authentication", "login"]
  }'
```

#### 搜索记忆
```bash
curl -X POST http://localhost:8080/api/memories/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "用户认证",
    "memory_types": ["code"],
    "limit": 10
  }'
```

#### 获取统计信息
```bash
curl http://localhost:8080/api/statistics
```

### 高级API

#### 创建对话记忆
```bash
curl -X POST http://localhost:8080/api/conversations \
  -H "Content-Type: application/json" \
  -d '{
    "content": "讨论了数据库设计方案",
    "participant": "user",
    "topic": "数据库设计"
  }'
```

#### 创建任务记忆
```bash
curl -X POST http://localhost:8080/api/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "name": "实现用户认证",
    "description": "添加JWT认证机制",
    "state": "in_progress"
  }'
```

#### 分析文件
```bash
curl -X POST http://localhost:8080/api/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "src/auth.py"
  }'
```

## Python API使用

### 基本使用

```python
from src.core.memory_manager import MemoryManager

# 初始化管理器
manager = MemoryManager()
manager.initialize()

# 添加记忆
memory_id = manager.add_memory(
    memory_type="code",
    content="实现了用户认证功能",
    context={"file": "auth.py", "function": "login"},
    tags=["authentication", "security"]
)

# 搜索记忆
memories = manager.search_memories("用户认证")
for memory in memories:
    print(f"{memory.memory_type}: {memory.content}")

# 获取统计信息
stats = manager.get_statistics()
print(f"总记忆数: {stats['total_entries']}")
```

### 高级使用

```python
from datetime import datetime, timedelta

# 按时间范围搜索
date_from = datetime.now() - timedelta(days=7)
recent_memories = manager.search_memories(
    date_from=date_from,
    memory_types=["code", "conversation"],
    limit=50
)

# 按标签搜索
auth_memories = manager.get_memories_by_tags(["authentication"])

# 导出记忆
manager.export_memories("backup.json", ["code", "task"])

# 清理过期记忆
deleted_count = manager.cleanup_old_memories()
```

## 配置说明

### 主要配置项

```yaml
# 数据库配置
database:
  path: "data/memory.db"
  backup_interval: 3600

# 监控配置
monitoring:
  project_root: "."
  watch_patterns:
    - "*.py"
    - "*.js"
    - "*.md"
  ignore_patterns:
    - "__pycache__"
    - ".git"

# 记忆管理
memory:
  max_entries: 10000
  retention_days: 30
  auto_tag: true
  deduplication: true

# API配置
api:
  host: "localhost"
  port: 8080
  enable_cors: true
```

### 自定义配置

1. 复制默认配置文件：
```bash
cp config/config.yaml my_config.yaml
```

2. 修改配置项

3. 使用自定义配置：
```bash
python src/main.py -c my_config.yaml
```

## 与Augment集成

### 作为Python模块

```python
# 在Augment脚本中导入
from src.core.memory_manager import MemoryManager

# 初始化
memory = MemoryManager(project_root="/path/to/augment/project")
memory.initialize()

# 记录Augment操作
memory.add_conversation_memory(
    content="用户请求实现登录功能",
    participant="user",
    topic="功能开发"
)

# 记录代码变更
memory.analyze_file("src/auth.py")
```

### 作为API服务

```python
import requests

# 记录对话
response = requests.post("http://localhost:8080/api/conversations", json={
    "content": "讨论了API设计方案",
    "participant": "augment_agent",
    "topic": "API设计"
})

# 搜索相关记忆
response = requests.post("http://localhost:8080/api/memories/search", json={
    "query": "API设计",
    "limit": 5
})
memories = response.json()["memories"]
```

## 故障排除

### 常见问题

1. **数据库锁定错误**
   - 确保只有一个AutoMem实例在运行
   - 检查数据库文件权限

2. **文件监控不工作**
   - 检查项目路径是否正确
   - 确认监控模式配置

3. **API连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置

4. **内存使用过高**
   - 调整max_entries配置
   - 定期运行cleanup命令

### 日志调试

```bash
# 启用调试日志
python src/main.py --log-level DEBUG --log-file logs/debug.log

# 查看日志
tail -f logs/debug.log
```
