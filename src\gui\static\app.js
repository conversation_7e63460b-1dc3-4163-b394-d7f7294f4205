// AutoMem GUI JavaScript

// 全局变量
let currentPage = 1;
let currentMemories = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    showDashboard();
});

// 显示加载指示器
function showLoading() {
    document.getElementById('loading').classList.remove('hidden');
}

// 隐藏加载指示器
function hideLoading() {
    document.getElementById('loading').classList.add('hidden');
}

// 显示消息
function showMessage(text, isError = false) {
    const message = document.getElementById('message');
    const messageText = document.getElementById('message-text');
    
    messageText.textContent = text;
    message.className = isError ? 'message error' : 'message';
    message.classList.remove('hidden');
    
    // 3秒后自动隐藏
    setTimeout(() => {
        hideMessage();
    }, 3000);
}

// 隐藏消息
function hideMessage() {
    document.getElementById('message').classList.add('hidden');
}

// 切换页面
function showPage(pageId) {
    // 隐藏所有页面
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // 移除所有导航按钮的active类
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 显示目标页面
    document.getElementById(pageId).classList.add('active');
}

// 显示仪表板
function showDashboard() {
    showPage('dashboard');
    document.querySelector('[onclick="showDashboard()"]').classList.add('active');
    loadDashboard();
}

// 显示记忆列表
function showMemories() {
    showPage('memories');
    document.querySelector('[onclick="showMemories()"]').classList.add('active');
    loadMemories();
}

// 显示搜索页面
function showSearch() {
    showPage('search');
    document.querySelector('[onclick="showSearch()"]').classList.add('active');
}

// 显示创建页面
function showCreate() {
    showPage('create');
    document.querySelector('[onclick="showCreate()"]').classList.add('active');
}

// 加载仪表板数据
async function loadDashboard() {
    try {
        showLoading();
        const response = await fetch('/api/dashboard');
        const data = await response.json();
        
        if (response.ok) {
            updateDashboard(data);
        } else {
            showMessage('加载仪表板数据失败: ' + data.error, true);
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, true);
    } finally {
        hideLoading();
    }
}

// 更新仪表板显示
function updateDashboard(data) {
    const stats = data.statistics;
    
    // 更新统计数字
    document.getElementById('total-memories').textContent = stats.total_entries || 0;
    document.getElementById('code-memories').textContent = stats.type_statistics?.code || 0;
    document.getElementById('conversation-memories').textContent = stats.type_statistics?.conversation || 0;
    document.getElementById('task-memories').textContent = stats.type_statistics?.task || 0;
    
    // 显示最近记忆
    const recentContainer = document.getElementById('recent-memories');
    recentContainer.innerHTML = '';
    
    if (data.recent_memories && data.recent_memories.length > 0) {
        data.recent_memories.forEach(memory => {
            recentContainer.appendChild(createMemoryElement(memory));
        });
    } else {
        recentContainer.innerHTML = '<p style="color: #718096; text-align: center;">暂无最近记忆</p>';
    }
}

// 加载记忆列表
async function loadMemories(page = 1) {
    try {
        showLoading();
        const typeFilter = document.getElementById('type-filter')?.value || '';
        const params = new URLSearchParams({
            page: page,
            limit: 20
        });
        
        if (typeFilter) {
            params.append('type', typeFilter);
        }
        
        const response = await fetch(`/api/memories?${params}`);
        const data = await response.json();
        
        if (response.ok) {
            currentMemories = data.memories;
            displayMemories(data.memories);
            displayPagination(data.pagination);
        } else {
            showMessage('加载记忆列表失败: ' + data.error, true);
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, true);
    } finally {
        hideLoading();
    }
}

// 显示记忆列表
function displayMemories(memories) {
    const container = document.getElementById('memories-list');
    container.innerHTML = '';
    
    if (memories.length === 0) {
        container.innerHTML = '<p style="color: #718096; text-align: center;">暂无记忆数据</p>';
        return;
    }
    
    memories.forEach(memory => {
        container.appendChild(createMemoryElement(memory));
    });
}

// 创建记忆元素
function createMemoryElement(memory) {
    const div = document.createElement('div');
    div.className = `memory-item ${memory.memory_type}`;
    div.onclick = () => showMemoryDetails(memory);
    
    const timestamp = new Date(memory.timestamp).toLocaleString('zh-CN');
    const tags = JSON.parse(memory.tags || '[]');
    
    div.innerHTML = `
        <div class="memory-header">
            <span class="memory-type">${getTypeDisplayName(memory.memory_type)}</span>
            <span class="memory-time">${timestamp}</span>
        </div>
        <div class="memory-content">${memory.content}</div>
        <div class="memory-tags">
            ${tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
        </div>
    `;
    
    return div;
}

// 获取类型显示名称
function getTypeDisplayName(type) {
    const typeNames = {
        'code': '代码',
        'conversation': '对话',
        'task': '任务',
        'file_change': '文件变更',
        'note': '笔记'
    };
    return typeNames[type] || type;
}

// 显示分页
function displayPagination(pagination) {
    const container = document.getElementById('pagination');
    container.innerHTML = '';
    
    if (pagination.pages <= 1) return;
    
    // 上一页按钮
    if (pagination.page > 1) {
        const prevBtn = document.createElement('button');
        prevBtn.textContent = '上一页';
        prevBtn.onclick = () => loadMemories(pagination.page - 1);
        container.appendChild(prevBtn);
    }
    
    // 页码按钮
    for (let i = 1; i <= pagination.pages; i++) {
        if (i === pagination.page || 
            i === 1 || 
            i === pagination.pages || 
            (i >= pagination.page - 2 && i <= pagination.page + 2)) {
            
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.className = i === pagination.page ? 'active' : '';
            pageBtn.onclick = () => loadMemories(i);
            container.appendChild(pageBtn);
        } else if (i === pagination.page - 3 || i === pagination.page + 3) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.style.padding = '0.5rem';
            container.appendChild(ellipsis);
        }
    }
    
    // 下一页按钮
    if (pagination.page < pagination.pages) {
        const nextBtn = document.createElement('button');
        nextBtn.textContent = '下一页';
        nextBtn.onclick = () => loadMemories(pagination.page + 1);
        container.appendChild(nextBtn);
    }
}

// 执行搜索
async function performSearch() {
    try {
        showLoading();
        
        const query = document.getElementById('search-query').value;
        const typeSelect = document.getElementById('search-type');
        const selectedTypes = Array.from(typeSelect.selectedOptions).map(option => option.value);
        const tagsInput = document.getElementById('search-tags').value;
        const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()) : [];
        
        const searchData = {
            query: query,
            memory_types: selectedTypes,
            tags: tags,
            limit: 50
        };
        
        const response = await fetch('/api/memories/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            displaySearchResults(data.memories);
        } else {
            showMessage('搜索失败: ' + data.error, true);
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, true);
    } finally {
        hideLoading();
    }
}

// 显示搜索结果
function displaySearchResults(memories) {
    const container = document.getElementById('search-results');
    container.innerHTML = '';
    
    if (memories.length === 0) {
        container.innerHTML = '<p style="color: #718096; text-align: center;">未找到匹配的记忆</p>';
        return;
    }
    
    const resultHeader = document.createElement('div');
    resultHeader.style.marginBottom = '1rem';
    resultHeader.innerHTML = `<h3>找到 ${memories.length} 条记忆</h3>`;
    container.appendChild(resultHeader);
    
    memories.forEach(memory => {
        container.appendChild(createMemoryElement(memory));
    });
}

// 创建记忆
async function createMemory(event) {
    event.preventDefault();
    
    try {
        showLoading();
        
        const type = document.getElementById('create-type').value;
        const content = document.getElementById('create-content').value;
        const tagsInput = document.getElementById('create-tags').value;
        const contextInput = document.getElementById('create-context').value;
        
        const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()) : [];
        let context = {};
        
        if (contextInput) {
            try {
                context = JSON.parse(contextInput);
            } catch (e) {
                showMessage('上下文JSON格式错误', true);
                return;
            }
        }
        
        const memoryData = {
            memory_type: type,
            content: content,
            tags: tags,
            context: context
        };
        
        const response = await fetch('/api/memories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(memoryData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            showMessage('记忆创建成功');
            // 清空表单
            document.getElementById('create-type').value = '';
            document.getElementById('create-content').value = '';
            document.getElementById('create-tags').value = '';
            document.getElementById('create-context').value = '';
        } else {
            showMessage('创建记忆失败: ' + data.error, true);
        }
    } catch (error) {
        showMessage('网络错误: ' + error.message, true);
    } finally {
        hideLoading();
    }
}

// 显示记忆详情
function showMemoryDetails(memory) {
    const modal = document.getElementById('memory-modal');
    const modalBody = document.getElementById('modal-body');
    
    const context = JSON.parse(memory.context || '{}');
    const tags = JSON.parse(memory.tags || '[]');
    
    modalBody.innerHTML = `
        <div style="margin-bottom: 1rem;">
            <strong>类型:</strong> ${getTypeDisplayName(memory.memory_type)}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>时间:</strong> ${new Date(memory.timestamp).toLocaleString('zh-CN')}
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>内容:</strong>
            <div style="background: #f7fafc; padding: 1rem; border-radius: 6px; margin-top: 0.5rem;">
                ${memory.content}
            </div>
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>标签:</strong>
            <div style="margin-top: 0.5rem;">
                ${tags.map(tag => `<span class="tag">${tag}</span>`).join(' ')}
            </div>
        </div>
        <div style="margin-bottom: 1rem;">
            <strong>上下文:</strong>
            <pre style="background: #f7fafc; padding: 1rem; border-radius: 6px; margin-top: 0.5rem; overflow-x: auto;">${JSON.stringify(context, null, 2)}</pre>
        </div>
    `;
    
    modal.style.display = 'block';
}

// 关闭模态框
function closeModal() {
    document.getElementById('memory-modal').style.display = 'none';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('memory-modal');
    if (event.target === modal) {
        closeModal();
    }
}
