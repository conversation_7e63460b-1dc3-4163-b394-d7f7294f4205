"""
AutoMem Web GUI 服务器
"""
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any
from http.server import <PERSON><PERSON>PServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

from ..core.memory_manager import MemoryManager


class WebGUIHandler(SimpleHTTPRequestHandler):
    """Web GUI 请求处理器"""
    
    def __init__(self, *args, memory_manager: MemoryManager = None, **kwargs):
        self.memory_manager = memory_manager
        self.logger = logging.getLogger(__name__)
        
        # 设置静态文件目录
        self.gui_dir = Path(__file__).parent / "static"
        super().__init__(*args, directory=str(self.gui_dir), **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        # API请求
        if path.startswith('/api/'):
            self._handle_api_request('GET', path, parse_qs(parsed_url.query))
        # 根路径重定向到index.html
        elif path == '/':
            self.path = '/index.html'
            super().do_GET()
        # 静态文件
        else:
            super().do_GET()
    
    def do_POST(self):
        """处理POST请求"""
        parsed_url = urlparse(self.path)
        path = parsed_url.path
        
        if path.startswith('/api/'):
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8')) if post_data else {}
            
            self._handle_api_request('POST', path, data)
        else:
            self.send_error(404, "Not Found")
    
    def _handle_api_request(self, method: str, path: str, data: Any):
        """处理API请求"""
        try:
            if path == '/api/memories' and method == 'GET':
                self._handle_get_memories(data)
            elif path == '/api/memories' and method == 'POST':
                self._handle_create_memory(data)
            elif path == '/api/memories/search' and method == 'POST':
                self._handle_search_memories(data)
            elif path == '/api/statistics' and method == 'GET':
                self._handle_get_statistics()
            elif path == '/api/dashboard' and method == 'GET':
                self._handle_get_dashboard()
            elif path.startswith('/api/memories/') and method == 'GET':
                memory_id = path.split('/')[-1]
                self._handle_get_memory(memory_id)
            else:
                self._send_error(404, "API endpoint not found")
                
        except Exception as e:
            self.logger.error(f"API请求处理失败: {e}")
            self._send_error(500, str(e))
    
    def _handle_get_memories(self, params: Dict[str, Any]):
        """获取记忆列表"""
        page = int(params.get('page', [1])[0])
        limit = int(params.get('limit', [20])[0])
        memory_type = params.get('type', [None])[0]
        
        offset = (page - 1) * limit
        
        # 获取记忆
        if memory_type:
            memories = self.memory_manager.get_memories_by_type(memory_type, limit + offset)
            memories = memories[offset:]
        else:
            from ..storage.models import SearchQuery
            query = SearchQuery(limit=limit, offset=offset)
            memories = self.memory_manager.db.search_memory_entries(query)
        
        # 获取总数
        stats = self.memory_manager.get_statistics()
        total = stats['total_entries']
        
        response_data = {
            'memories': [memory.to_dict() for memory in memories],
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            }
        }
        
        self._send_json_response(response_data)
    
    def _handle_create_memory(self, data: Dict[str, Any]):
        """创建记忆"""
        memory_type = data.get('memory_type', '')
        content = data.get('content', '')
        context = data.get('context', {})
        tags = data.get('tags', [])
        
        if not memory_type or not content:
            self._send_error(400, "memory_type and content are required")
            return
        
        memory_id = self.memory_manager.add_memory(memory_type, content, context, tags)
        
        response_data = {
            'id': memory_id,
            'message': 'Memory created successfully'
        }
        self._send_json_response(response_data, 201)
    
    def _handle_search_memories(self, data: Dict[str, Any]):
        """搜索记忆"""
        query = data.get('query', '')
        memory_types = data.get('memory_types', [])
        tags = data.get('tags', [])
        limit = data.get('limit', 50)
        
        memories = self.memory_manager.search_memories(
            query=query,
            memory_types=memory_types,
            tags=tags,
            limit=limit
        )
        
        response_data = {
            'memories': [memory.to_dict() for memory in memories],
            'total': len(memories),
            'query': data
        }
        
        self._send_json_response(response_data)
    
    def _handle_get_memory(self, memory_id: str):
        """获取单个记忆"""
        memory = self.memory_manager.get_memory(memory_id)
        if memory:
            self._send_json_response(memory.to_dict())
        else:
            self._send_error(404, "Memory not found")
    
    def _handle_get_statistics(self):
        """获取统计信息"""
        stats = self.memory_manager.get_statistics()
        self._send_json_response(stats)
    
    def _handle_get_dashboard(self):
        """获取仪表板数据"""
        stats = self.memory_manager.get_statistics()
        recent_memories = self.memory_manager.get_recent_memories(7, 10)
        
        dashboard_data = {
            'statistics': stats,
            'recent_memories': [memory.to_dict() for memory in recent_memories],
            'memory_types': list(stats.get('type_statistics', {}).keys()),
            'total_memories': stats['total_entries']
        }
        
        self._send_json_response(dashboard_data)
    
    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def _send_error(self, status_code: int, message: str):
        """发送错误响应"""
        error_data = {
            'error': message,
            'status_code': status_code
        }
        self._send_json_response(error_data, status_code)
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()


class WebGUIServer:
    """Web GUI 服务器"""
    
    def __init__(self, memory_manager: MemoryManager, host: str = 'localhost', port: int = 8888):
        self.memory_manager = memory_manager
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.logger = logging.getLogger(__name__)
        
        # 确保静态文件目录存在
        self._ensure_static_files()
    
    def _ensure_static_files(self):
        """确保静态文件存在"""
        static_dir = Path(__file__).parent / "static"
        static_dir.mkdir(exist_ok=True)
        
        # 如果index.html不存在，创建一个基本的
        index_file = static_dir / "index.html"
        if not index_file.exists():
            self._create_basic_html_files()
    
    def _create_basic_html_files(self):
        """创建基本的HTML文件"""
        static_dir = Path(__file__).parent / "static"
        
        # 创建index.html
        index_html = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoMem 记忆管理系统</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <header>
            <h1>AutoMem 记忆管理系统</h1>
            <nav>
                <button onclick="showDashboard()">仪表板</button>
                <button onclick="showMemories()">记忆列表</button>
                <button onclick="showSearch()">搜索</button>
                <button onclick="showCreate()">创建记忆</button>
            </nav>
        </header>
        
        <main id="content">
            <div id="loading">加载中...</div>
        </main>
    </div>
    
    <script src="app.js"></script>
</body>
</html>'''
        
        with open(static_dir / "index.html", 'w', encoding='utf-8') as f:
            f.write(index_html)
        
        self.logger.info("创建了基本的HTML文件")
    
    def start(self):
        """启动Web GUI服务器"""
        def handler(*args, **kwargs):
            return WebGUIHandler(*args, memory_manager=self.memory_manager, **kwargs)
        
        self.server = HTTPServer((self.host, self.port), handler)
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        self.logger.info(f"Web GUI服务器启动: http://{self.host}:{self.port}")
    
    def stop(self):
        """停止Web GUI服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.logger.info("Web GUI服务器已停止")
    
    def is_running(self) -> bool:
        """检查服务器是否运行中"""
        return self.server_thread and self.server_thread.is_alive()
