"""
AutoMem 主程序入口
"""
import os
import sys
import argparse
import logging
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.memory_manager import MemoryManager
from src.api.memory_api import MemoryAPIServer
from src.gui.web_server import WebGUIServer


def setup_logging(log_level: str = "INFO", log_file: str = None):
    """设置日志配置"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 创建日志目录
    if log_file:
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AutoMem - 记忆增强系统")
    parser.add_argument("--config", "-c", default="config/config.yaml", help="配置文件路径")
    parser.add_argument("--project-root", "-p", default=".", help="项目根目录")
    parser.add_argument("--log-level", "-l", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="日志级别")
    parser.add_argument("--log-file", help="日志文件路径")
    parser.add_argument("--api-only", action="store_true", help="仅启动API服务器")
    parser.add_argument("--no-api", action="store_true", help="不启动API服务器")
    parser.add_argument("--host", default="localhost", help="API服务器主机")
    parser.add_argument("--port", type=int, default=8080, help="API服务器端口")
    parser.add_argument("--gui", action="store_true", help="启动Web GUI界面")
    parser.add_argument("--gui-port", type=int, default=8888, help="GUI服务器端口")
    parser.add_argument("--mcp", action="store_true", help="启动MCP STDIO服务器模式")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 初始化命令
    init_parser = subparsers.add_parser("init", help="初始化记忆系统")
    init_parser.add_argument("--force", action="store_true", help="强制重新初始化")
    
    # 分析命令
    analyze_parser = subparsers.add_parser("analyze", help="分析文件或目录")
    analyze_parser.add_argument("path", help="要分析的文件或目录路径")
    
    # 搜索命令
    search_parser = subparsers.add_parser("search", help="搜索记忆")
    search_parser.add_argument("query", help="搜索关键词")
    search_parser.add_argument("--type", help="记忆类型过滤")
    search_parser.add_argument("--tags", help="标签过滤（逗号分隔）")
    search_parser.add_argument("--limit", type=int, default=10, help="结果数量限制")
    
    # 统计命令
    stats_parser = subparsers.add_parser("stats", help="显示统计信息")
    
    # 导出命令
    export_parser = subparsers.add_parser("export", help="导出记忆数据")
    export_parser.add_argument("output", help="输出文件路径")
    export_parser.add_argument("--type", help="记忆类型过滤")
    
    # 清理命令
    cleanup_parser = subparsers.add_parser("cleanup", help="清理过期记忆")
    cleanup_parser.add_argument("--days", type=int, default=30, help="保留天数")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger(__name__)
    
    try:
        # 初始化记忆管理器
        memory_manager = MemoryManager(args.config, args.project_root)
        
        # 处理命令
        if args.command == "init":
            logger.info("初始化记忆系统...")
            memory_manager.initialize()
            logger.info("初始化完成")
            
        elif args.command == "analyze":
            logger.info(f"分析路径: {args.path}")
            if Path(args.path).is_file():
                memory = memory_manager.analyze_file(args.path)
                if memory:
                    print(f"分析完成，记忆ID: {memory.id}")
                else:
                    print("分析失败")
            else:
                print("目前只支持单个文件分析")
                
        elif args.command == "search":
            logger.info(f"搜索记忆: {args.query}")
            memory_types = [args.type] if args.type else []
            tags = args.tags.split(',') if args.tags else []
            
            memories = memory_manager.search_memories(
                query=args.query,
                memory_types=memory_types,
                tags=tags,
                limit=args.limit
            )
            
            print(f"找到 {len(memories)} 条记忆:")
            for memory in memories:
                print(f"- [{memory.memory_type}] {memory.content[:100]}...")
                
        elif args.command == "stats":
            stats = memory_manager.get_statistics()
            print("=== 记忆系统统计 ===")
            print(f"总记忆数: {stats['total_entries']}")
            print(f"项目数: {stats['total_projects']}")
            print(f"最新记忆: {stats['latest_memory']}")
            print(f"监控状态: {'活跃' if stats['monitoring_active'] else '停止'}")
            print("\n按类型统计:")
            for mem_type, count in stats['type_statistics'].items():
                print(f"  {mem_type}: {count}")
                
        elif args.command == "export":
            logger.info(f"导出记忆到: {args.output}")
            memory_types = [args.type] if args.type else []
            success = memory_manager.export_memories(args.output, memory_types)
            if success:
                print("导出成功")
            else:
                print("导出失败")
                
        elif args.command == "cleanup":
            logger.info(f"清理 {args.days} 天前的记忆")
            deleted_count = memory_manager.cleanup_old_memories()
            print(f"清理了 {deleted_count} 条过期记忆")
            
        elif args.mcp:
            # MCP STDIO服务器模式
            logger.info("启动AutoMem MCP STDIO服务器")
            import asyncio
            from src.mcp.stdio_server import STDIOServer

            try:
                stdio_server = STDIOServer()
                asyncio.run(stdio_server.run())
            except Exception as e:
                logger.error(f"MCP服务器启动失败: {e}")
                sys.exit(1)

        else:
            # 默认模式：启动完整系统
            logger.info("启动AutoMem记忆增强系统")

            if not args.api_only:
                memory_manager.initialize()

            # 启动API服务器
            api_server = None
            if not args.no_api:
                api_server = MemoryAPIServer(memory_manager, args.host, args.port)
                api_server.start()

            # 启动GUI服务器
            gui_server = None
            if args.gui:
                gui_server = WebGUIServer(memory_manager, args.host, args.gui_port)
                gui_server.start()
                logger.info(f"Web GUI已启动: http://{args.host}:{args.gui_port}")

            # 设置信号处理
            def signal_handler(signum, frame):
                logger.info("接收到停止信号，正在关闭...")
                if api_server:
                    api_server.stop()
                if gui_server:
                    gui_server.stop()
                memory_manager.shutdown()
                sys.exit(0)

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            if api_server or gui_server:
                logger.info("系统运行中，按 Ctrl+C 停止")
                if gui_server:
                    logger.info(f"访问 Web GUI: http://{args.host}:{args.gui_port}")
                try:
                    # 保持主线程运行
                    while True:
                        import time
                        time.sleep(1)
                        if api_server and not api_server.is_running():
                            break
                        if gui_server and not gui_server.is_running():
                            break
                except KeyboardInterrupt:
                    pass
            else:
                logger.info("记忆系统已启动（仅监控模式）")
                try:
                    while True:
                        import time
                        time.sleep(1)
                except KeyboardInterrupt:
                    pass

            # 清理
            if api_server:
                api_server.stop()
            if gui_server:
                gui_server.stop()
            memory_manager.shutdown()
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
