"""
上下文收集器测试
"""
import unittest
import tempfile
import os
from pathlib import Path
import json

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.context_collector import ContextCollector
from src.utils.text_processor import TextProcessor


class TestContextCollector(unittest.TestCase):
    """上下文收集器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = Path(self.temp_dir)
        
        # 创建测试文件结构
        self.create_test_files()
        
        self.collector = ContextCollector(str(self.project_root))
    
    def tearDown(self):
        """测试后清理"""
        self.collector.stop_monitoring()
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def create_test_files(self):
        """创建测试文件"""
        # Python文件
        python_file = self.project_root / "test.py"
        python_file.write_text('''
def hello_world():
    """测试函数"""
    print("Hello, World!")

class TestClass:
    def __init__(self):
        self.name = "test"
    
    def method(self):
        return self.name
''')
        
        # JavaScript文件
        js_file = self.project_root / "test.js"
        js_file.write_text('''
function greet(name) {
    return `Hello, ${name}!`;
}

const user = {
    name: "John",
    age: 30
};
''')
        
        # 配置文件
        config_file = self.project_root / "package.json"
        config_file.write_text(json.dumps({
            "name": "test-project",
            "dependencies": {
                "react": "^18.0.0",
                "express": "^4.18.0"
            }
        }))
        
        # requirements.txt
        req_file = self.project_root / "requirements.txt"
        req_file.write_text("pytest>=7.0.0\nrequests>=2.28.0\n")
    
    def test_collect_initial_context(self):
        """测试收集初始项目上下文"""
        context = self.collector.collect_initial_context()
        
        self.assertIsNotNone(context)
        self.assertEqual(context.project_path, str(self.project_root))
        self.assertIsInstance(context.file_structure, dict)
        self.assertIsInstance(context.dependencies, list)
        
        # 检查依赖分析
        self.assertIn("react", context.dependencies)
        self.assertIn("pytest", context.dependencies)
    
    def test_collect_file_memory(self):
        """测试收集文件记忆"""
        python_file = self.project_root / "test.py"
        memory = self.collector.collect_file_memory(str(python_file))
        
        self.assertIsNotNone(memory)
        self.assertEqual(memory.memory_type, "code")
        self.assertIn("hello_world", memory.context['structure']['functions'][0])
        self.assertIn("TestClass", memory.context['structure']['classes'][0])
        self.assertEqual(memory.context['language'], "python")
    
    def test_collect_conversation_memory(self):
        """测试收集对话记忆"""
        conversation_data = {
            'content': '讨论了API设计方案，决定使用REST风格',
            'participant': 'user',
            'topic': 'API设计'
        }
        
        memory = self.collector.collect_conversation_memory(conversation_data)
        
        self.assertIsNotNone(memory)
        self.assertEqual(memory.memory_type, "conversation")
        self.assertEqual(memory.context['participant'], 'user')
        self.assertEqual(memory.context['topic'], 'API设计')
        self.assertIn('conversation', memory.tags)
    
    def test_collect_task_memory(self):
        """测试收集任务记忆"""
        task_data = {
            'id': 'task-001',
            'name': '实现用户认证',
            'description': '添加JWT认证机制',
            'state': 'in_progress'
        }
        
        memory = self.collector.collect_task_memory(task_data)
        
        self.assertIsNotNone(memory)
        self.assertEqual(memory.memory_type, "task")
        self.assertEqual(memory.context['name'], '实现用户认证')
        self.assertEqual(memory.context['state'], 'in_progress')
        self.assertIn('task', memory.tags)
    
    def test_analyze_dependencies(self):
        """测试依赖分析"""
        dependencies = self.collector._analyze_dependencies()
        
        # 应该检测到Node.js和Python依赖
        self.assertIn("react", dependencies)
        self.assertIn("express", dependencies)
        self.assertIn("pytest", dependencies)
        self.assertIn("requests", dependencies)
    
    def test_detect_project_type(self):
        """测试项目类型检测"""
        project_type = self.collector._detect_project_type()
        
        # 由于有package.json，应该检测为nodejs项目
        self.assertEqual(project_type, "nodejs")
    
    def test_detect_languages(self):
        """测试语言检测"""
        languages = self.collector._detect_languages()
        
        self.assertIn("python", languages)
        self.assertIn("javascript", languages)
        self.assertIn("json", languages)


class TestTextProcessor(unittest.TestCase):
    """文本处理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = TextProcessor()
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        text = "这是一个测试文本，包含了用户认证和数据库设计的内容"
        keywords = self.processor.extract_keywords(text)
        
        self.assertIsInstance(keywords, list)
        self.assertIn("用户认证", keywords)
        self.assertIn("数据库设计", keywords)
    
    def test_detect_language(self):
        """测试语言检测"""
        self.assertEqual(self.processor.detect_language("test.py"), "python")
        self.assertEqual(self.processor.detect_language("test.js"), "javascript")
        self.assertEqual(self.processor.detect_language("test.md"), "markdown")
        self.assertEqual(self.processor.detect_language("test.unknown"), "text")
    
    def test_extract_python_structure(self):
        """测试Python代码结构提取"""
        python_code = '''
import os
from datetime import datetime

def hello_world():
    """测试函数"""
    print("Hello, World!")

class TestClass:
    def __init__(self):
        self.name = "test"
    
    def method(self):
        return self.name

# 这是注释
'''
        
        structure = self.processor.extract_code_structure(python_code, "python")
        
        self.assertIn("hello_world", structure['functions'][0])
        self.assertIn("TestClass", structure['classes'][0])
        self.assertIn("import os", structure['imports'])
        self.assertIn("# 这是注释", structure['comments'])
    
    def test_extract_javascript_structure(self):
        """测试JavaScript代码结构提取"""
        js_code = '''
const express = require('express');
import React from 'react';

function greet(name) {
    return `Hello, ${name}!`;
}

const arrow = () => {
    console.log("Arrow function");
};

class Component {
    constructor() {
        this.state = {};
    }
}

// 这是注释
'''
        
        structure = self.processor.extract_code_structure(js_code, "javascript")
        
        self.assertTrue(any("greet" in func for func in structure['functions']))
        self.assertTrue(any("Component" in cls for cls in structure['classes']))
        self.assertTrue(any("express" in imp for imp in structure['imports']))
    
    def test_summarize_content(self):
        """测试内容摘要"""
        long_text = "这是一个很长的文本。" * 100
        summary = self.processor.summarize_content(long_text, 50)
        
        self.assertLessEqual(len(summary), 53)  # 50 + "..."
        self.assertTrue(summary.endswith("..."))
    
    def test_generate_auto_tags(self):
        """测试自动标签生成"""
        content = "这是一个Python函数，实现了用户认证功能"
        file_path = "auth.py"
        
        tags = self.processor.generate_auto_tags(content, file_path)
        
        self.assertIn("python", tags)
        self.assertIn("auth", tags)
        self.assertIn("用户认证", tags)


if __name__ == '__main__':
    unittest.main()
