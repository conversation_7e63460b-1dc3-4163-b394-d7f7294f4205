#!/usr/bin/env python3
"""
AutoMem 演示脚本
"""
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.memory_manager import MemoryManager
from src.api.memory_api import MemoryAPIServer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

def demo_basic_usage():
    """演示基本使用"""
    print("=" * 60)
    print("AutoMem 基本功能演示")
    print("=" * 60)
    
    # 初始化记忆管理器
    print("1. 初始化记忆管理器...")
    manager = MemoryManager(project_root=".")
    manager.initialize()
    
    # 添加记忆
    print("\n2. 添加记忆...")
    memory_id1 = manager.add_memory(
        memory_type="code",
        content="实现了用户认证功能，包括登录、注册和密码重置",
        context={"file": "auth.py", "function": "login"},
        tags=["authentication", "security", "login"]
    )
    print(f"   添加代码记忆: {memory_id1}")
    
    memory_id2 = manager.add_conversation_memory(
        content="讨论了数据库设计方案，决定使用PostgreSQL作为主数据库",
        participant="user",
        topic="数据库设计"
    )
    print(f"   添加对话记忆: {memory_id2}")
    
    memory_id3 = manager.add_task_memory(
        task_name="实现API接口",
        description="创建REST API接口，支持CRUD操作",
        state="in_progress"
    )
    print(f"   添加任务记忆: {memory_id3}")
    
    # 搜索记忆
    print("\n3. 搜索记忆...")
    
    # 按关键词搜索
    memories = manager.search_memories("用户认证")
    print(f"   关键词'用户认证'搜索结果: {len(memories)}条")
    for memory in memories:
        print(f"     - [{memory.memory_type}] {memory.content[:50]}...")
    
    # 按类型搜索
    code_memories = manager.get_memories_by_type("code")
    print(f"   代码类型记忆: {len(code_memories)}条")
    
    # 按标签搜索
    auth_memories = manager.get_memories_by_tags(["authentication"])
    print(f"   认证标签记忆: {len(auth_memories)}条")
    
    # 获取统计信息
    print("\n4. 统计信息...")
    stats = manager.get_statistics()
    print(f"   总记忆数: {stats['total_entries']}")
    print(f"   项目数: {stats['total_projects']}")
    print(f"   监控状态: {'活跃' if stats['monitoring_active'] else '停止'}")
    print("   按类型统计:")
    for mem_type, count in stats['type_statistics'].items():
        print(f"     {mem_type}: {count}")
    
    # 分析当前文件
    print("\n5. 分析文件...")
    current_file = __file__
    memory = manager.analyze_file(current_file)
    if memory:
        print(f"   分析文件 {Path(current_file).name}: {memory.id}")
        print(f"   检测语言: {memory.context.get('language', 'unknown')}")
        print(f"   文件大小: {memory.context.get('file_size', 0)} bytes")
    
    # 导出记忆
    print("\n6. 导出记忆...")
    export_file = "demo_memories.json"
    success = manager.export_memories(export_file)
    if success:
        print(f"   记忆已导出到: {export_file}")
    
    # 清理
    print("\n7. 清理...")
    manager.shutdown()
    print("   记忆管理器已关闭")

def demo_api_server():
    """演示API服务器"""
    print("\n" + "=" * 60)
    print("AutoMem API服务器演示")
    print("=" * 60)
    
    # 初始化记忆管理器
    print("1. 启动API服务器...")
    manager = MemoryManager(project_root=".")
    manager.initialize()
    
    # 启动API服务器
    api_server = MemoryAPIServer(manager, 'localhost', 8080)
    api_server.start()
    
    print("   API服务器已启动: http://localhost:8080")
    print("   可用端点:")
    print("     GET  /api/health          - 健康检查")
    print("     GET  /api/memories        - 获取记忆列表")
    print("     POST /api/memories        - 创建记忆")
    print("     POST /api/memories/search - 搜索记忆")
    print("     GET  /api/statistics      - 获取统计信息")
    print("     POST /api/conversations   - 创建对话记忆")
    print("     POST /api/tasks           - 创建任务记忆")
    print("     POST /api/analyze         - 分析文件")
    
    print("\n2. 示例API调用:")
    print("   # 健康检查")
    print("   curl http://localhost:8080/api/health")
    print()
    print("   # 创建记忆")
    print("   curl -X POST http://localhost:8080/api/memories \\")
    print("     -H 'Content-Type: application/json' \\")
    print("     -d '{\"memory_type\":\"test\",\"content\":\"测试记忆\"}'")
    print()
    print("   # 搜索记忆")
    print("   curl -X POST http://localhost:8080/api/memories/search \\")
    print("     -H 'Content-Type: application/json' \\")
    print("     -d '{\"query\":\"测试\"}'")
    
    try:
        print("\n3. 服务器运行中...")
        print("   按 Ctrl+C 停止服务器")
        
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n4. 停止服务器...")
        api_server.stop()
        manager.shutdown()
        print("   服务器已停止")

def demo_file_monitoring():
    """演示文件监控"""
    print("\n" + "=" * 60)
    print("AutoMem 文件监控演示")
    print("=" * 60)
    
    # 初始化记忆管理器
    print("1. 启动文件监控...")
    manager = MemoryManager(project_root=".")
    manager.initialize()
    
    print("   文件监控已启动")
    print("   监控模式: *.py, *.js, *.md, *.yaml, *.json")
    print("   忽略模式: __pycache__, .git, node_modules")
    
    print("\n2. 创建测试文件来触发监控...")
    
    # 创建测试文件
    test_file = Path("test_monitoring.py")
    test_content = '''
def test_function():
    """这是一个测试函数"""
    print("Hello from monitoring test!")
    return True

class TestClass:
    def __init__(self):
        self.name = "test"
'''
    
    print(f"   创建文件: {test_file}")
    test_file.write_text(test_content)
    
    # 等待文件监控处理
    time.sleep(2)
    
    # 修改文件
    print(f"   修改文件: {test_file}")
    modified_content = test_content + '''
    
    def another_function():
        """另一个函数"""
        return "modified"
'''
    test_file.write_text(modified_content)
    
    # 等待处理
    time.sleep(2)
    
    # 查看生成的记忆
    print("\n3. 查看监控生成的记忆...")
    file_memories = manager.search_memories(str(test_file))
    print(f"   找到 {len(file_memories)} 条相关记忆")
    
    for memory in file_memories:
        print(f"     - [{memory.memory_type}] {memory.context.get('change_type', 'analysis')}: {memory.content[:50]}...")
    
    # 删除测试文件
    print(f"\n4. 删除测试文件: {test_file}")
    test_file.unlink()
    
    # 等待处理
    time.sleep(2)
    
    # 清理
    print("\n5. 停止监控...")
    manager.shutdown()
    print("   文件监控已停止")

def main():
    """主函数"""
    setup_logging()
    
    if len(sys.argv) > 1:
        demo_type = sys.argv[1]
        
        if demo_type == "basic":
            demo_basic_usage()
        elif demo_type == "api":
            demo_api_server()
        elif demo_type == "monitor":
            demo_file_monitoring()
        else:
            print(f"未知演示类型: {demo_type}")
            print("可用类型: basic, api, monitor")
    else:
        print("AutoMem 演示脚本")
        print("使用方法:")
        print("  python demo.py basic    - 基本功能演示")
        print("  python demo.py api      - API服务器演示")
        print("  python demo.py monitor  - 文件监控演示")

if __name__ == '__main__':
    main()
