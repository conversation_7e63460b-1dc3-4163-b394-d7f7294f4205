"""
SQLite数据库操作模块
"""
import sqlite3
import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import uuid

from .models import MemoryEntry, ProjectContext, SearchQuery


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data/memory.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._ensure_db_directory()
        self._init_database()
    
    def _ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建记忆条目表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS memory_entries (
                    id TEXT PRIMARY KEY,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    context TEXT,
                    tags TEXT,
                    timestamp TEXT NOT NULL,
                    content_hash TEXT NOT NULL,
                    project_path TEXT,
                    UNIQUE(content_hash)
                )
            ''')
            
            # 创建项目上下文表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS project_contexts (
                    id TEXT PRIMARY KEY,
                    project_path TEXT UNIQUE NOT NULL,
                    file_structure TEXT,
                    dependencies TEXT,
                    last_updated TEXT NOT NULL,
                    metadata TEXT
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries(memory_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON memory_entries(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_hash ON memory_entries(content_hash)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_project_path ON memory_entries(project_path)')
            
            conn.commit()
            self.logger.info("数据库初始化完成")
    
    def add_memory_entry(self, entry: MemoryEntry) -> str:
        """添加记忆条目"""
        if not entry.id:
            entry.id = str(uuid.uuid4())
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                data = entry.to_dict()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO memory_entries 
                    (id, memory_type, content, context, tags, timestamp, content_hash, project_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['id'], data['memory_type'], data['content'],
                    data['context'], data['tags'], data['timestamp'],
                    data['content_hash'], data['project_path']
                ))
                
                conn.commit()
                self.logger.info(f"添加记忆条目: {entry.id}")
                return entry.id
                
        except sqlite3.IntegrityError as e:
            if "UNIQUE constraint failed" in str(e):
                self.logger.warning(f"记忆条目已存在，跳过重复添加: {entry.content_hash}")
                return self._get_entry_id_by_hash(entry.content_hash)
            else:
                raise e
    
    def _get_entry_id_by_hash(self, content_hash: str) -> str:
        """根据哈希值获取条目ID"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM memory_entries WHERE content_hash = ?', (content_hash,))
            result = cursor.fetchone()
            return result[0] if result else ""
    
    def get_memory_entry(self, entry_id: str) -> Optional[MemoryEntry]:
        """获取单个记忆条目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM memory_entries WHERE id = ?', (entry_id,))
            row = cursor.fetchone()

            if row:
                columns = [desc[0] for desc in cursor.description]
                data = dict(zip(columns, row))
                return MemoryEntry.from_dict(data)
            return None

    def search_memory_entries(self, query: SearchQuery) -> List[MemoryEntry]:
        """搜索记忆条目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 构建SQL查询
            sql_parts = ["SELECT * FROM memory_entries WHERE 1=1"]
            params = []

            # 关键词搜索
            if query.keywords:
                keyword_conditions = []
                for keyword in query.keywords:
                    keyword_conditions.append("(content LIKE ? OR context LIKE ?)")
                    params.extend([f"%{keyword}%", f"%{keyword}%"])
                sql_parts.append(f"AND ({' OR '.join(keyword_conditions)})")

            # 记忆类型过滤
            if query.memory_types:
                placeholders = ','.join(['?' for _ in query.memory_types])
                sql_parts.append(f"AND memory_type IN ({placeholders})")
                params.extend(query.memory_types)

            # 标签过滤
            if query.tags:
                tag_conditions = []
                for tag in query.tags:
                    tag_conditions.append("tags LIKE ?")
                    params.append(f"%{tag}%")
                sql_parts.append(f"AND ({' OR '.join(tag_conditions)})")

            # 日期范围过滤
            if query.date_from:
                sql_parts.append("AND timestamp >= ?")
                params.append(query.date_from.isoformat())

            if query.date_to:
                sql_parts.append("AND timestamp <= ?")
                params.append(query.date_to.isoformat())

            # 项目路径过滤
            if query.project_path:
                sql_parts.append("AND project_path = ?")
                params.append(query.project_path)

            # 排序和分页
            sql_parts.append("ORDER BY timestamp DESC")
            sql_parts.append("LIMIT ? OFFSET ?")
            params.extend([query.limit, query.offset])

            sql = " ".join(sql_parts)
            cursor.execute(sql, params)

            results = []
            columns = [desc[0] for desc in cursor.description]
            for row in cursor.fetchall():
                data = dict(zip(columns, row))
                results.append(MemoryEntry.from_dict(data))

            return results

    def delete_memory_entry(self, entry_id: str) -> bool:
        """删除记忆条目"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM memory_entries WHERE id = ?', (entry_id,))
            conn.commit()
            return cursor.rowcount > 0

    def update_memory_entry(self, entry: MemoryEntry) -> bool:
        """更新记忆条目"""
        if not entry.id:
            return False

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            data = entry.to_dict()

            cursor.execute('''
                UPDATE memory_entries
                SET memory_type=?, content=?, context=?, tags=?,
                    timestamp=?, content_hash=?, project_path=?
                WHERE id=?
            ''', (
                data['memory_type'], data['content'], data['context'],
                data['tags'], data['timestamp'], data['content_hash'],
                data['project_path'], data['id']
            ))

            conn.commit()
            return cursor.rowcount > 0

    def add_project_context(self, context: ProjectContext) -> str:
        """添加项目上下文"""
        if not context.id:
            context.id = str(uuid.uuid4())

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            data = context.to_dict()

            cursor.execute('''
                INSERT OR REPLACE INTO project_contexts
                (id, project_path, file_structure, dependencies, last_updated, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                data['id'], data['project_path'], data['file_structure'],
                data['dependencies'], data['last_updated'], data['metadata']
            ))

            conn.commit()
            self.logger.info(f"添加项目上下文: {context.project_path}")
            return context.id

    def get_project_context(self, project_path: str) -> Optional[ProjectContext]:
        """获取项目上下文"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM project_contexts WHERE project_path = ?', (project_path,))
            row = cursor.fetchone()

            if row:
                columns = [desc[0] for desc in cursor.description]
                data = dict(zip(columns, row))
                return ProjectContext.from_dict(data)
            return None

    def get_all_project_contexts(self) -> List[ProjectContext]:
        """获取所有项目上下文"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM project_contexts ORDER BY last_updated DESC')

            results = []
            columns = [desc[0] for desc in cursor.description]
            for row in cursor.fetchall():
                data = dict(zip(columns, row))
                results.append(ProjectContext.from_dict(data))

            return results

    def cleanup_old_entries(self, retention_days: int = 30) -> int:
        """清理过期的记忆条目"""
        cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        cutoff_date = cutoff_date.replace(day=cutoff_date.day - retention_days)

        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'DELETE FROM memory_entries WHERE timestamp < ?',
                (cutoff_date.isoformat(),)
            )
            deleted_count = cursor.rowcount
            conn.commit()

            self.logger.info(f"清理了 {deleted_count} 个过期记忆条目")
            return deleted_count

    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # 总记忆条目数
            cursor.execute('SELECT COUNT(*) FROM memory_entries')
            total_entries = cursor.fetchone()[0]

            # 按类型统计
            cursor.execute('''
                SELECT memory_type, COUNT(*)
                FROM memory_entries
                GROUP BY memory_type
            ''')
            type_stats = dict(cursor.fetchall())

            # 项目数量
            cursor.execute('SELECT COUNT(*) FROM project_contexts')
            total_projects = cursor.fetchone()[0]

            # 最新记忆时间
            cursor.execute('SELECT MAX(timestamp) FROM memory_entries')
            latest_memory = cursor.fetchone()[0]

            return {
                'total_entries': total_entries,
                'type_statistics': type_stats,
                'total_projects': total_projects,
                'latest_memory': latest_memory,
                'database_path': self.db_path
            }
