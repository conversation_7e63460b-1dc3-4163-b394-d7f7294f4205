"""
记忆管理器 - 核心记忆管理功能
"""
import logging
import yaml
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from ..storage.database import DatabaseManager
from ..storage.models import MemoryEntry, ProjectContext, SearchQuery
from .context_collector import ContextCollector


class MemoryManager:
    """记忆管理器"""
    
    def __init__(self, config_path: str = "config/config.yaml", project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 初始化数据库
        db_path = self.config.get('database', {}).get('path', 'data/memory.db')
        self.db = DatabaseManager(db_path)
        
        # 初始化上下文收集器
        self.context_collector = ContextCollector(
            str(self.project_root),
            self.config.get('context', {})
        )
        
        # 设置回调
        self.context_collector.add_memory_callback(self._on_memory_collected)
        self.context_collector.add_context_callback(self._on_context_collected)
        
        self.logger.info("记忆管理器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            self.logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return self._get_default_config()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'database': {'path': 'data/memory.db'},
            'memory': {
                'max_entries': 10000,
                'retention_days': 30,
                'auto_tag': True,
                'deduplication': True
            },
            'context': {
                'max_content_length': 5000,
                'collect_code_structure': True,
                'collect_file_changes': True
            }
        }
    
    def initialize(self):
        """初始化系统"""
        self.logger.info("开始初始化记忆增强系统")
        
        # 收集初始项目上下文
        initial_context = self.context_collector.collect_initial_context()
        self.logger.info(f"收集到初始项目上下文: {initial_context.project_path}")
        
        # 开始监控
        self.context_collector.start_monitoring()
        self.logger.info("记忆增强系统初始化完成")
    
    def shutdown(self):
        """关闭系统"""
        self.context_collector.stop_monitoring()
        self.logger.info("记忆增强系统已关闭")
    
    def add_memory(self, memory_type: str, content: str, context: Dict[str, Any] = None,
                   tags: List[str] = None) -> str:
        """手动添加记忆"""
        memory = MemoryEntry(
            memory_type=memory_type,
            content=content,
            context=context or {},
            tags=tags or [],
            project_path=str(self.project_root)
        )
        
        # 自动生成标签
        if self.config.get('memory', {}).get('auto_tag', True) and not tags:
            from ..utils.text_processor import TextProcessor
            processor = TextProcessor()
            memory.tags = processor.generate_auto_tags(content)
        
        return self.db.add_memory_entry(memory)
    
    def search_memories(self, query: str = "", memory_types: List[str] = None,
                       tags: List[str] = None, date_from: datetime = None,
                       date_to: datetime = None, limit: int = 100) -> List[MemoryEntry]:
        """搜索记忆"""
        search_query = SearchQuery(
            keywords=[query] if query else [],
            memory_types=memory_types or [],
            tags=tags or [],
            date_from=date_from,
            date_to=date_to,
            project_path=str(self.project_root),
            limit=limit
        )
        
        return self.db.search_memory_entries(search_query)
    
    def get_memory(self, memory_id: str) -> Optional[MemoryEntry]:
        """获取单个记忆"""
        return self.db.get_memory_entry(memory_id)
    
    def update_memory(self, memory: MemoryEntry) -> bool:
        """更新记忆"""
        return self.db.update_memory_entry(memory)
    
    def delete_memory(self, memory_id: str) -> bool:
        """删除记忆"""
        return self.db.delete_memory_entry(memory_id)
    
    def get_recent_memories(self, days: int = 7, limit: int = 50) -> List[MemoryEntry]:
        """获取最近的记忆"""
        date_from = datetime.now() - timedelta(days=days)
        return self.search_memories(date_from=date_from, limit=limit)
    
    def get_memories_by_type(self, memory_type: str, limit: int = 100) -> List[MemoryEntry]:
        """按类型获取记忆"""
        return self.search_memories(memory_types=[memory_type], limit=limit)
    
    def get_memories_by_tags(self, tags: List[str], limit: int = 100) -> List[MemoryEntry]:
        """按标签获取记忆"""
        return self.search_memories(tags=tags, limit=limit)
    
    def add_conversation_memory(self, content: str, participant: str = "user",
                              topic: str = "") -> str:
        """添加对话记忆"""
        conversation_data = {
            'content': content,
            'participant': participant,
            'topic': topic,
            'timestamp': datetime.now().isoformat()
        }
        
        memory = self.context_collector.collect_conversation_memory(conversation_data)
        return memory.id
    
    def add_task_memory(self, task_name: str, description: str, state: str = "created") -> str:
        """添加任务记忆"""
        task_data = {
            'name': task_name,
            'description': description,
            'state': state,
            'created_time': datetime.now().isoformat()
        }
        
        memory = self.context_collector.collect_task_memory(task_data)
        return memory.id
    
    def analyze_file(self, file_path: str) -> Optional[MemoryEntry]:
        """分析单个文件并添加记忆"""
        return self.context_collector.collect_file_memory(file_path, "analysis")
    
    def get_project_context(self) -> Optional[ProjectContext]:
        """获取项目上下文"""
        return self.db.get_project_context(str(self.project_root))
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        db_stats = self.db.get_statistics()
        
        # 添加额外统计信息
        recent_memories = self.get_recent_memories(7)
        
        stats = {
            **db_stats,
            'recent_memories_count': len(recent_memories),
            'project_root': str(self.project_root),
            'monitoring_active': self.context_collector.file_monitor.is_monitoring
        }
        
        return stats
    
    def cleanup_old_memories(self) -> int:
        """清理过期记忆"""
        retention_days = self.config.get('memory', {}).get('retention_days', 30)
        return self.db.cleanup_old_entries(retention_days)
    
    def export_memories(self, output_path: str, memory_types: List[str] = None) -> bool:
        """导出记忆数据"""
        try:
            memories = self.search_memories(memory_types=memory_types, limit=10000)
            
            export_data = {
                'export_time': datetime.now().isoformat(),
                'project_root': str(self.project_root),
                'total_memories': len(memories),
                'memories': [memory.to_dict() for memory in memories]
            }
            
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"导出 {len(memories)} 条记忆到 {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出记忆失败: {e}")
            return False
    
    def _on_memory_collected(self, memory: MemoryEntry):
        """记忆收集回调"""
        try:
            # 检查去重
            if self.config.get('memory', {}).get('deduplication', True):
                existing_id = self.db._get_entry_id_by_hash(memory.content_hash)
                if existing_id:
                    self.logger.debug(f"跳过重复记忆: {memory.content_hash}")
                    return
            
            # 添加到数据库
            memory_id = self.db.add_memory_entry(memory)
            self.logger.info(f"自动收集记忆: {memory_id} - {memory.memory_type}")
            
        except Exception as e:
            self.logger.error(f"处理收集的记忆失败: {e}")
    
    def _on_context_collected(self, context: ProjectContext):
        """上下文收集回调"""
        try:
            context_id = self.db.add_project_context(context)
            self.logger.info(f"更新项目上下文: {context_id}")
            
        except Exception as e:
            self.logger.error(f"处理收集的上下文失败: {e}")
