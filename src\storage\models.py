"""
数据模型定义
"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Any, Optional
import hashlib
import json


@dataclass
class MemoryEntry:
    """记忆条目数据模型"""
    id: Optional[str] = None
    memory_type: str = ""  # code, conversation, task, file_change, context
    content: str = ""
    context: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    content_hash: str = ""
    project_path: str = ""
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.content_hash:
            self.content_hash = self._generate_hash()
    
    def _generate_hash(self) -> str:
        """生成内容哈希值用于去重"""
        content_str = f"{self.memory_type}:{self.content}:{json.dumps(self.context, sort_keys=True)}"
        return hashlib.md5(content_str.encode('utf-8')).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'memory_type': self.memory_type,
            'content': self.content,
            'context': json.dumps(self.context),
            'tags': json.dumps(self.tags),
            'timestamp': self.timestamp.isoformat(),
            'content_hash': self.content_hash,
            'project_path': self.project_path
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """从字典创建实例"""
        return cls(
            id=data.get('id'),
            memory_type=data.get('memory_type', ''),
            content=data.get('content', ''),
            context=json.loads(data.get('context', '{}')),
            tags=json.loads(data.get('tags', '[]')),
            timestamp=datetime.fromisoformat(data.get('timestamp', datetime.now().isoformat())),
            content_hash=data.get('content_hash', ''),
            project_path=data.get('project_path', '')
        )


@dataclass
class ProjectContext:
    """项目上下文数据模型"""
    id: Optional[str] = None
    project_path: str = ""
    file_structure: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'project_path': self.project_path,
            'file_structure': json.dumps(self.file_structure),
            'dependencies': json.dumps(self.dependencies),
            'last_updated': self.last_updated.isoformat(),
            'metadata': json.dumps(self.metadata)
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectContext':
        """从字典创建实例"""
        return cls(
            id=data.get('id'),
            project_path=data.get('project_path', ''),
            file_structure=json.loads(data.get('file_structure', '{}')),
            dependencies=json.loads(data.get('dependencies', '[]')),
            last_updated=datetime.fromisoformat(data.get('last_updated', datetime.now().isoformat())),
            metadata=json.loads(data.get('metadata', '{}'))
        )


@dataclass
class SearchQuery:
    """搜索查询模型"""
    keywords: List[str] = field(default_factory=list)
    memory_types: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    project_path: Optional[str] = None
    limit: int = 100
    offset: int = 0
