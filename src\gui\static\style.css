/* AutoMem GUI 样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header-content h1 {
    color: #4a5568;
    font-size: 1.8rem;
    margin-bottom: 0.2rem;
}

.header-content p {
    color: #718096;
    font-size: 0.9rem;
}

.nav {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.nav-btn {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.nav-btn.active {
    background: #4299e1;
    border-color: #4299e1;
    color: white;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.page {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.page-header h2 {
    color: #2d3748;
    font-size: 1.5rem;
}

/* 仪表板样式 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #4299e1; }
.stat-card:nth-child(2) .stat-icon { background: #48bb78; }
.stat-card:nth-child(3) .stat-icon { background: #ed8936; }
.stat-card:nth-child(4) .stat-icon { background: #9f7aea; }

.stat-info h3 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 0.2rem;
}

.stat-info p {
    color: #718096;
    font-size: 0.9rem;
}

/* 最近记忆区域 */
.recent-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.recent-section h2 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* 记忆列表样式 */
.memory-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.memory-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #e2e8f0;
    transition: all 0.2s ease;
    cursor: pointer;
}

.memory-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.memory-item.code { border-left-color: #48bb78; }
.memory-item.conversation { border-left-color: #ed8936; }
.memory-item.task { border-left-color: #9f7aea; }
.memory-item.file_change { border-left-color: #4299e1; }

.memory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.memory-type {
    background: #f7fafc;
    color: #4a5568;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.memory-time {
    color: #718096;
    font-size: 0.8rem;
}

.memory-content {
    color: #2d3748;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.memory-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: #edf2f7;
    color: #4a5568;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #4a5568;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4299e1;
}

.search-form,
.create-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #4299e1;
    color: white;
}

.btn-primary:hover {
    background: #3182ce;
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
}

/* 过滤器样式 */
.filters {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.filters select {
    padding: 0.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination button {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination button:hover {
    background: #f7fafc;
}

.pagination button.active {
    background: #4299e1;
    border-color: #4299e1;
    color: white;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.close {
    color: #718096;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #2d3748;
}

/* 加载和消息样式 */
.loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-align: center;
    z-index: 1000;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4299e1;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: #48bb78;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.message.error {
    background: #f56565;
}

.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .filters {
        justify-content: center;
    }
}
