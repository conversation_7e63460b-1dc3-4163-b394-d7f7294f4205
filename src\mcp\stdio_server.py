#!/usr/bin/env python3
"""
AutoMem MCP STDIO 服务器
用于与Augment Agent通过标准输入输出进行通信
"""
import sys
import json
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.memory_manager import MemoryManager
from src.mcp.server import MCPServer


class STDIOServer:
    """STDIO MCP服务器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 设置日志输出到文件，避免干扰stdio通信
        log_file = project_root / "logs" / "mcp_server.log"
        log_file.parent.mkdir(exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.INFO)
        
        # 初始化记忆管理器
        try:
            config_path = project_root / "config" / "config.yaml"
            self.memory_manager = MemoryManager(str(config_path), str(project_root))
            self.memory_manager.initialize()
            
            # 创建MCP服务器
            self.mcp_server = MCPServer(self.memory_manager)
            
            self.logger.info("AutoMem MCP服务器初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            raise
    
    async def run(self):
        """运行STDIO服务器"""
        self.logger.info("启动AutoMem MCP STDIO服务器")
        
        try:
            while True:
                # 从stdin读取请求
                line = await self._read_line()
                if not line:
                    break
                
                try:
                    request = json.loads(line)
                    self.logger.info(f"收到请求: {request.get('method', 'unknown')}")
                    
                    # 处理请求
                    response = await self.mcp_server.handle_request(request)
                    
                    # 发送响应
                    await self._write_response(response)
                    
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON解析错误: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    await self._write_response(error_response)
                    
                except Exception as e:
                    self.logger.error(f"处理请求失败: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32603,
                            "message": f"Internal error: {str(e)}"
                        }
                    }
                    await self._write_response(error_response)
                    
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭服务器")
        except Exception as e:
            self.logger.error(f"服务器运行错误: {e}")
        finally:
            await self._cleanup()
    
    async def _read_line(self) -> str:
        """从stdin读取一行"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, sys.stdin.readline)
    
    async def _write_response(self, response: dict):
        """写入响应到stdout"""
        response_json = json.dumps(response, ensure_ascii=False)
        print(response_json, flush=True)
        self.logger.info(f"发送响应: {response.get('result', {}).get('method', 'unknown')}")
    
    async def _cleanup(self):
        """清理资源"""
        try:
            self.memory_manager.shutdown()
            self.logger.info("AutoMem MCP服务器已关闭")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")


async def main():
    """主函数"""
    server = STDIOServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
