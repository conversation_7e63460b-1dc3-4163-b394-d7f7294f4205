#!/usr/bin/env python3
"""
AutoMem 常见问题修复脚本
"""
import sys
import os
import subprocess
from pathlib import Path

def fix_requirements():
    """修复requirements.txt文件"""
    print("🔧 修复requirements.txt文件...")
    
    correct_requirements = """pyyaml>=6.0
watchdog>=3.0.0
python-dateutil>=2.8.0
requests>=2.28.0"""
    
    with open("requirements.txt", "w", encoding="utf-8") as f:
        f.write(correct_requirements)
    
    print("✅ requirements.txt已修复")

def fix_import_paths():
    """修复导入路径问题"""
    print("🔧 检查导入路径...")
    
    # 确保项目根目录在Python路径中
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    print("✅ 导入路径已修复")

def fix_permissions():
    """修复文件权限问题（Windows通常不需要）"""
    if os.name != 'nt':  # 非Windows系统
        print("🔧 修复文件权限...")
        
        scripts = [
            "src/main.py",
            "src/mcp/stdio_server.py",
            "start_automem.py",
            "install.py",
            "demo.py"
        ]
        
        for script in scripts:
            if Path(script).exists():
                os.chmod(script, 0o755)
        
        print("✅ 文件权限已修复")

def create_missing_directories():
    """创建缺失的目录"""
    print("🔧 创建缺失的目录...")
    
    directories = [
        "data",
        "logs", 
        "src/gui/static",
        "augment_integration",
        "tests",
        "docs"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构已修复")

def fix_config_file():
    """修复配置文件"""
    print("🔧 检查配置文件...")
    
    config_file = Path("config/config.yaml")
    if not config_file.exists():
        print("⚠️ 配置文件不存在，将使用默认配置")
    else:
        print("✅ 配置文件存在")

def fix_path_issues():
    """修复路径相关问题"""
    print("🔧 修复路径问题...")

    # 检查并修复可能的路径问题
    try:
        from pathlib import Path

        # 测试Path对象的方法
        test_path = Path(".")

        # 确保使用正确的方法名
        if hasattr(test_path, 'is_dir'):
            print("✅ Path.is_dir() 方法可用")
        else:
            print("❌ Path.is_dir() 方法不可用")

        if hasattr(test_path, 'is_file'):
            print("✅ Path.is_file() 方法可用")
        else:
            print("❌ Path.is_file() 方法不可用")

        return True

    except Exception as e:
        print(f"❌ 路径问题修复失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")

    try:
        # 测试导入
        from src.storage.models import MemoryEntry
        from src.storage.database import DatabaseManager

        # 测试数据库
        test_db_path = "data/test_fix.db"
        db = DatabaseManager(test_db_path)

        # 测试添加记忆
        memory = MemoryEntry(
            memory_type="test",
            content="测试记忆",
            project_path="."
        )

        memory_id = db.add_memory_entry(memory)

        # 测试检索
        retrieved = db.get_memory_entry(memory_id)

        if retrieved and retrieved.content == "测试记忆":
            print("✅ 基本功能测试通过")

            # 清理测试文件
            Path(test_db_path).unlink(missing_ok=True)
            return True
        else:
            print("❌ 基本功能测试失败")
            return False

    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False

def check_python_environment():
    """检查Python环境"""
    print("🔍 检查Python环境...")
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查关键模块
    modules_to_check = ['json', 'sqlite3', 'pathlib', 'logging', 'datetime']
    
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {module} 可用")
        except ImportError:
            print(f"❌ {module} 不可用")

def main():
    """主修复流程"""
    print("🛠️ AutoMem 问题修复工具")
    print("=" * 40)
    
    # 1. 检查Python环境
    check_python_environment()
    print()
    
    # 2. 修复requirements.txt
    fix_requirements()
    print()
    
    # 3. 修复导入路径
    fix_import_paths()
    print()
    
    # 4. 创建缺失目录
    create_missing_directories()
    print()
    
    # 5. 修复路径问题
    fix_path_issues()
    print()

    # 6. 修复权限
    fix_permissions()
    print()

    # 7. 检查配置
    fix_config_file()
    print()

    # 8. 测试功能
    if test_basic_functionality():
        print("\n🎉 所有问题已修复!")
        print("\n建议的下一步:")
        print("1. 重新安装依赖: python install.py")
        print("2. 初始化系统: python src/main.py init")
        print("3. 启动系统: python start_automem.py --mode full")
    else:
        print("\n⚠️ 仍有问题需要解决")
        print("请检查错误信息并手动修复")

if __name__ == "__main__":
    main()
