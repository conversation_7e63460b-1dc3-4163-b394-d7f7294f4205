"""
文件监控工具
"""
import os
import time
import logging
from pathlib import Path
from typing import List, Dict, Any, Callable, Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent
import fnmatch


class FileChangeHandler(FileSystemEventHandler):
    """文件变更处理器"""
    
    def __init__(self, callback: Callable[[str, str, str], None], 
                 watch_patterns: List[str], ignore_patterns: List[str]):
        self.callback = callback
        self.watch_patterns = watch_patterns
        self.ignore_patterns = ignore_patterns
        self.logger = logging.getLogger(__name__)
    
    def _should_process_file(self, file_path: str) -> bool:
        """判断是否应该处理该文件"""
        file_name = os.path.basename(file_path)
        
        # 检查忽略模式
        for pattern in self.ignore_patterns:
            if fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(file_path, pattern):
                return False
        
        # 检查监控模式
        if not self.watch_patterns:
            return True
        
        for pattern in self.watch_patterns:
            if fnmatch.fnmatch(file_name, pattern):
                return True
        
        return False
    
    def on_modified(self, event: FileSystemEvent):
        """文件修改事件"""
        if not event.is_directory and self._should_process_file(event.src_path):
            self.callback(event.src_path, "modified", "")
    
    def on_created(self, event: FileSystemEvent):
        """文件创建事件"""
        if not event.is_directory and self._should_process_file(event.src_path):
            self.callback(event.src_path, "created", "")
    
    def on_deleted(self, event: FileSystemEvent):
        """文件删除事件"""
        if not event.is_directory and self._should_process_file(event.src_path):
            self.callback(event.src_path, "deleted", "")
    
    def on_moved(self, event: FileSystemEvent):
        """文件移动事件"""
        if hasattr(event, 'dest_path') and not event.is_directory:
            if self._should_process_file(event.src_path):
                self.callback(event.src_path, "moved", event.dest_path)


class FileMonitor:
    """文件监控器"""
    
    def __init__(self, project_root: str, watch_patterns: List[str] = None, 
                 ignore_patterns: List[str] = None):
        self.project_root = Path(project_root).resolve()
        self.watch_patterns = watch_patterns or ["*"]
        self.ignore_patterns = ignore_patterns or []
        self.observer = Observer()
        self.is_monitoring = False
        self.logger = logging.getLogger(__name__)
        self.change_callbacks: List[Callable] = []
    
    def add_change_callback(self, callback: Callable[[str, str, str], None]):
        """添加文件变更回调函数"""
        self.change_callbacks.append(callback)
    
    def _on_file_change(self, file_path: str, change_type: str, dest_path: str = ""):
        """文件变更回调"""
        self.logger.info(f"文件变更: {change_type} - {file_path}")
        
        for callback in self.change_callbacks:
            try:
                callback(file_path, change_type, dest_path)
            except Exception as e:
                self.logger.error(f"文件变更回调执行失败: {e}")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        handler = FileChangeHandler(
            self._on_file_change,
            self.watch_patterns,
            self.ignore_patterns
        )
        
        self.observer.schedule(handler, str(self.project_root), recursive=True)
        self.observer.start()
        self.is_monitoring = True
        self.logger.info(f"开始监控目录: {self.project_root}")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.observer.stop()
        self.observer.join()
        self.is_monitoring = False
        self.logger.info("停止文件监控")
    
    def get_file_structure(self) -> Dict[str, Any]:
        """获取当前文件结构"""
        def scan_directory(path: Path) -> Dict[str, Any]:
            structure = {
                'type': 'directory',
                'name': path.name,
                'path': str(path.relative_to(self.project_root)),
                'children': {}
            }
            
            try:
                for item in path.iterdir():
                    # 跳过隐藏文件和忽略的文件
                    if item.name.startswith('.'):
                        continue
                    
                    should_ignore = False
                    for pattern in self.ignore_patterns:
                        if fnmatch.fnmatch(item.name, pattern):
                            should_ignore = True
                            break
                    
                    if should_ignore:
                        continue
                    
                    if item.is_directory():
                        structure['children'][item.name] = scan_directory(item)
                    else:
                        structure['children'][item.name] = {
                            'type': 'file',
                            'name': item.name,
                            'path': str(item.relative_to(self.project_root)),
                            'size': item.stat().st_size,
                            'modified': item.stat().st_mtime
                        }
            except PermissionError:
                self.logger.warning(f"无权限访问目录: {path}")
            
            return structure
        
        return scan_directory(self.project_root)
