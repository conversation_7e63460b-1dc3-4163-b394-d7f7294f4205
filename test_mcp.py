#!/usr/bin/env python3
"""
测试MCP服务器是否正常工作
"""
import sys
import os
import subprocess
import json
from pathlib import Path

def test_mcp_server():
    """测试MCP服务器"""
    print("🧪 测试AutoMem MCP服务器")
    print("=" * 40)
    
    # 检查文件是否存在
    project_root = Path(__file__).parent
    mcp_server_file = project_root / "start_mcp_server.py"
    
    if not mcp_server_file.exists():
        print("❌ start_mcp_server.py 文件不存在")
        return False
    
    print("✅ MCP服务器启动脚本存在")
    
    # 检查依赖
    try:
        sys.path.insert(0, str(project_root))
        from src.mcp.stdio_server import STDIOServer
        print("✅ MCP服务器模块导入成功")
    except ImportError as e:
        print(f"❌ MCP服务器模块导入失败: {e}")
        return False
    
    # 测试配置文件
    config_file = project_root / "augment_integration" / "mcp_config_fixed.json"
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ MCP配置文件格式正确")
            print(f"   配置路径: {config['mcpServers']['automem']['cwd']}")
        except Exception as e:
            print(f"❌ MCP配置文件有问题: {e}")
            return False
    else:
        print("⚠️ 使用默认MCP配置")
    
    print("\n🎯 MCP服务器测试完成")
    print("\n下一步:")
    print("1. 将 augment_integration/mcp_config_fixed.json 的内容")
    print("   复制到您的Augment配置文件中")
    print("2. 重启Augment Agent")
    print("3. 测试MCP工具是否可用")
    
    return True

def show_config():
    """显示正确的配置"""
    print("\n📋 正确的Augment MCP配置:")
    print("=" * 40)
    
    config = {
        "mcpServers": {
            "automem": {
                "command": "python",
                "args": ["start_mcp_server.py"],
                "cwd": "C:/Users/<USER>/Desktop/project/AutoMem",
                "env": {
                    "PYTHONPATH": "C:/Users/<USER>/Desktop/project/AutoMem"
                }
            }
        }
    }
    
    print(json.dumps(config, indent=2))
    
    print("\n📝 配置步骤:")
    print("1. 复制上面的JSON配置")
    print("2. 添加到您的Augment配置文件中")
    print("3. 确保路径指向正确的AutoMem项目目录")
    print("4. 重启Augment Agent")

def main():
    """主函数"""
    if test_mcp_server():
        show_config()
        
        print("\n🚀 手动测试MCP服务器:")
        print("python start_mcp_server.py")
        
        # 询问是否立即测试
        response = input("\n是否现在测试MCP服务器? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("\n启动MCP服务器（按Ctrl+C停止）...")
            try:
                subprocess.run([sys.executable, "start_mcp_server.py"])
            except KeyboardInterrupt:
                print("\n✅ MCP服务器测试完成")
    else:
        print("\n❌ MCP服务器测试失败")
        print("请先运行: python fix_common_issues.py")

if __name__ == "__main__":
    main()
