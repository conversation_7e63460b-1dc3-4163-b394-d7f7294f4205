#!/usr/bin/env python3
"""
AutoMem GUI管理器启动脚本
一键启动图形化管理界面
"""
import sys
import subprocess
import os
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    missing_deps = []
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter (Python GUI库)")
    
    try:
        import psutil
    except ImportError:
        missing_deps.append("psutil")
    
    return missing_deps

def install_dependencies():
    """安装缺失的依赖"""
    print("🔧 安装缺失的依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil>=5.9.0"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 AutoMem GUI管理器启动器")
    print("=" * 40)
    
    # 检查依赖
    missing_deps = check_dependencies()
    
    if missing_deps:
        print("⚠️ 缺少以下依赖:")
        for dep in missing_deps:
            print(f"  - {dep}")
        
        if 'psutil' in str(missing_deps):
            response = input("\n是否自动安装缺失的依赖? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                if not install_dependencies():
                    print("❌ 无法自动安装依赖，请手动安装:")
                    print("pip install psutil>=5.9.0")
                    sys.exit(1)
            else:
                print("请手动安装依赖后重试")
                sys.exit(1)
        
        if 'tkinter' in str(missing_deps):
            print("❌ tkinter是Python标准库的一部分，如果缺失请重新安装Python")
            sys.exit(1)
    
    # 检查AutoMem文件
    required_files = [
        "automem_manager.py",
        "stop_automem.py", 
        "quick_start.py",
        "src/main.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下必需文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("请确保在AutoMem项目根目录中运行此脚本")
        sys.exit(1)
    
    print("✅ 所有依赖和文件检查通过")
    print("🖥️ 启动AutoMem GUI管理器...")
    
    try:
        # 启动GUI管理器
        subprocess.run([sys.executable, "automem_manager.py"])
    except KeyboardInterrupt:
        print("\n👋 GUI管理器已关闭")
    except Exception as e:
        print(f"\n❌ 启动GUI管理器失败: {e}")
        print("\n故障排除:")
        print("1. 确保在AutoMem项目根目录中")
        print("2. 检查Python环境是否正确")
        print("3. 运行: python diagnose_issue.py")

if __name__ == "__main__":
    main()
