#!/usr/bin/env python3
"""
AutoMem 问题诊断脚本
专门用于诊断初始化失败的问题
"""
import sys
import traceback
from pathlib import Path

def test_pathlib():
    """测试pathlib功能"""
    print("🔍 测试pathlib功能...")
    
    try:
        test_path = Path(".")
        
        # 测试基本方法
        print(f"✅ Path创建成功: {test_path}")
        print(f"✅ resolve(): {test_path.resolve()}")
        print(f"✅ exists(): {test_path.exists()}")
        print(f"✅ is_dir(): {test_path.is_dir()}")
        print(f"✅ is_file(): {test_path.is_file()}")
        
        # 测试rglob
        files = list(test_path.rglob('*.py'))
        print(f"✅ rglob('*.py'): 找到 {len(files)} 个Python文件")
        
        return True
        
    except Exception as e:
        print(f"❌ pathlib测试失败: {e}")
        traceback.print_exc()
        return False

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试模块导入...")
    
    modules_to_test = [
        'src.storage.models',
        'src.storage.database', 
        'src.core.context_collector',
        'src.core.memory_manager',
        'src.utils.file_monitor',
        'src.utils.text_processor'
    ]
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module} 导入成功")
        except Exception as e:
            print(f"❌ {module} 导入失败: {e}")
            traceback.print_exc()
            return False
    
    return True

def test_memory_manager_init():
    """测试MemoryManager初始化"""
    print("🔍 测试MemoryManager初始化...")
    
    try:
        from src.core.memory_manager import MemoryManager
        
        print("✅ MemoryManager导入成功")
        
        # 尝试创建实例
        manager = MemoryManager(project_root=".")
        print("✅ MemoryManager实例创建成功")
        
        # 尝试初始化
        manager.initialize()
        print("✅ MemoryManager初始化成功")
        
        # 清理
        manager.shutdown()
        print("✅ MemoryManager关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ MemoryManager测试失败: {e}")
        traceback.print_exc()
        return False

def test_context_collector():
    """测试ContextCollector"""
    print("🔍 测试ContextCollector...")
    
    try:
        from src.core.context_collector import ContextCollector
        
        print("✅ ContextCollector导入成功")
        
        # 创建实例
        collector = ContextCollector(".")
        print("✅ ContextCollector实例创建成功")
        
        # 测试收集初始上下文
        context = collector.collect_initial_context()
        print("✅ 收集初始上下文成功")
        
        # 清理
        collector.stop_monitoring()
        print("✅ ContextCollector停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ ContextCollector测试失败: {e}")
        traceback.print_exc()
        return False

def test_file_monitor():
    """测试FileMonitor"""
    print("🔍 测试FileMonitor...")
    
    try:
        from src.utils.file_monitor import FileMonitor
        
        print("✅ FileMonitor导入成功")
        
        # 创建实例
        monitor = FileMonitor(".")
        print("✅ FileMonitor实例创建成功")
        
        # 测试获取文件结构
        structure = monitor.get_file_structure()
        print("✅ 获取文件结构成功")
        print(f"   文件结构类型: {structure.get('type')}")
        print(f"   子项数量: {len(structure.get('children', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ FileMonitor测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主诊断流程"""
    print("🩺 AutoMem 问题诊断")
    print("=" * 40)
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    tests = [
        ("pathlib功能", test_pathlib),
        ("模块导入", test_imports),
        ("FileMonitor", test_file_monitor),
        ("ContextCollector", test_context_collector),
        ("MemoryManager", test_memory_manager_init)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            traceback.print_exc()
            results.append((test_name, False))
    
    print(f"\n{'='*60}")
    print("📊 诊断结果汇总")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    failed_tests = [name for name, result in results if not result]
    
    if failed_tests:
        print(f"\n⚠️ 失败的测试: {', '.join(failed_tests)}")
        print("\n建议的修复步骤:")
        print("1. 运行: python fix_common_issues.py")
        print("2. 重新安装依赖: python install.py")
        print("3. 检查Python版本是否为3.8+")
    else:
        print("\n🎉 所有测试通过！系统应该可以正常工作。")

if __name__ == "__main__":
    main()
