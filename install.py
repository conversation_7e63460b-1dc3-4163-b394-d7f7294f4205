#!/usr/bin/env python3
"""
AutoMem 安装脚本
自动检查和安装依赖，初始化系统
"""
import sys
import subprocess
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: AutoMem需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    
    # 需要安装的包
    packages = [
        "pyyaml>=6.0",
        "watchdog>=3.0.0", 
        "python-dateutil>=2.8.0",
        "requests>=2.28.0"
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def check_dependencies():
    """检查依赖是否已安装"""
    print("🔍 检查依赖...")
    
    required_modules = {
        'yaml': 'pyyaml',
        'watchdog': 'watchdog',
        'dateutil': 'python-dateutil',
        'requests': 'requests'
    }
    
    missing = []
    
    for module, package in required_modules.items():
        try:
            __import__(module)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing.append(package)
    
    return len(missing) == 0, missing

def create_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    
    directories = [
        "data",
        "logs",
        "config"
    ]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {dir_name}")
        else:
            print(f"✅ 目录已存在: {dir_name}")

def initialize_config():
    """初始化配置文件"""
    print("⚙️ 初始化配置...")
    
    config_file = Path("config/config.yaml")
    if not config_file.exists():
        print("✅ 配置文件已存在")
    else:
        print("✅ 使用默认配置文件")

def test_installation():
    """测试安装是否成功"""
    print("🧪 测试安装...")
    
    try:
        # 测试导入核心模块
        sys.path.insert(0, str(Path.cwd()))
        
        from src.storage.models import MemoryEntry
        from src.storage.database import DatabaseManager
        
        print("✅ 核心模块导入成功")
        
        # 测试数据库初始化
        db = DatabaseManager("data/test.db")
        print("✅ 数据库初始化成功")
        
        # 清理测试文件
        test_db = Path("data/test.db")
        if test_db.exists():
            test_db.unlink()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("🚀 AutoMem 安装程序")
    print("=" * 50)
    
    # 1. 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 2. 检查依赖
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"\n缺少依赖: {', '.join(missing)}")
        
        # 询问是否自动安装
        response = input("\n是否自动安装缺少的依赖? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            if not install_dependencies():
                print("❌ 依赖安装失败")
                sys.exit(1)
        else:
            print("请手动安装依赖:")
            print("pip install pyyaml watchdog python-dateutil requests")
            sys.exit(1)
    
    # 3. 创建目录
    create_directories()
    
    # 4. 初始化配置
    initialize_config()
    
    # 5. 测试安装
    if not test_installation():
        print("❌ 安装测试失败")
        sys.exit(1)
    
    print("\n🎉 AutoMem 安装完成!")
    print("\n下一步:")
    print("1. 初始化系统: python src/main.py init")
    print("2. 启动完整系统: python start_automem.py --mode full --open-browser")
    print("3. 查看使用指南: docs/USAGE.md")
    print("4. Augment集成: augment_integration/INTEGRATION_GUIDE.md")

if __name__ == "__main__":
    main()
