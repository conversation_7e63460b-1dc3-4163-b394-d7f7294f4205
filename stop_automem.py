#!/usr/bin/env python3
"""
AutoMem 停止脚本
用于停止正在运行的AutoMem服务
"""
import sys
import os
import signal
import subprocess
import psutil
import time
from pathlib import Path

def find_automem_processes():
    """查找AutoMem相关进程"""
    automem_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('automem' in str(arg).lower() or 
                             'src/main.py' in str(arg) or
                             'start_mcp_server.py' in str(arg) or
                             'quick_start.py' in str(arg)
                             for arg in cmdline):
                automem_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return automem_processes

def find_processes_by_port(ports):
    """根据端口查找进程"""
    port_processes = []
    
    for conn in psutil.net_connections():
        if conn.laddr and conn.laddr.port in ports:
            try:
                proc = psutil.Process(conn.pid)
                port_processes.append((proc, conn.laddr.port))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    
    return port_processes

def stop_process_gracefully(proc, timeout=10):
    """优雅地停止进程"""
    try:
        print(f"正在停止进程 {proc.pid} ({proc.name()})...")
        
        # 首先尝试发送SIGTERM信号
        if os.name == 'nt':  # Windows
            proc.terminate()
        else:  # Unix/Linux
            proc.send_signal(signal.SIGTERM)
        
        # 等待进程结束
        try:
            proc.wait(timeout=timeout)
            print(f"✅ 进程 {proc.pid} 已正常停止")
            return True
        except psutil.TimeoutExpired:
            print(f"⚠️ 进程 {proc.pid} 未在 {timeout} 秒内停止，强制终止...")
            proc.kill()
            proc.wait(timeout=5)
            print(f"✅ 进程 {proc.pid} 已强制停止")
            return True
            
    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
        print(f"❌ 无法停止进程 {proc.pid}: {e}")
        return False

def stop_automem_by_process():
    """通过进程名停止AutoMem"""
    print("🔍 查找AutoMem相关进程...")
    
    processes = find_automem_processes()
    
    if not processes:
        print("ℹ️ 未找到运行中的AutoMem进程")
        return True
    
    print(f"找到 {len(processes)} 个AutoMem相关进程:")
    for proc in processes:
        try:
            cmdline = ' '.join(proc.info['cmdline'])
            print(f"  PID {proc.pid}: {cmdline}")
        except:
            print(f"  PID {proc.pid}: {proc.name()}")
    
    success = True
    for proc in processes:
        if not stop_process_gracefully(proc):
            success = False
    
    return success

def stop_automem_by_port():
    """通过端口停止AutoMem"""
    print("🔍 查找占用AutoMem端口的进程...")
    
    # AutoMem常用端口
    automem_ports = [8080, 8888, 8081, 8889]
    
    port_processes = find_processes_by_port(automem_ports)
    
    if not port_processes:
        print("ℹ️ 未找到占用AutoMem端口的进程")
        return True
    
    print(f"找到 {len(port_processes)} 个占用AutoMem端口的进程:")
    for proc, port in port_processes:
        try:
            print(f"  PID {proc.pid} 占用端口 {port}: {proc.name()}")
        except:
            print(f"  PID {proc.pid} 占用端口 {port}")
    
    success = True
    for proc, port in port_processes:
        print(f"停止占用端口 {port} 的进程...")
        if not stop_process_gracefully(proc):
            success = False
    
    return success

def cleanup_temp_files():
    """清理临时文件"""
    print("🧹 清理临时文件...")
    
    temp_files = [
        "data/test.db",
        "data/test_fix.db",
        "test_monitoring.py"
    ]
    
    for file_path in temp_files:
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                print(f"✅ 删除临时文件: {file_path}")
        except Exception as e:
            print(f"⚠️ 无法删除 {file_path}: {e}")

def main():
    """主函数"""
    print("🛑 AutoMem 停止脚本")
    print("=" * 30)
    
    # 检查是否有管理员权限（Windows）
    if os.name == 'nt':
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if not is_admin:
                print("⚠️ 建议以管理员身份运行以确保能停止所有进程")
        except:
            pass
    
    success = True
    
    # 方法1: 通过进程名停止
    if not stop_automem_by_process():
        success = False
    
    print()
    
    # 方法2: 通过端口停止
    if not stop_automem_by_port():
        success = False
    
    print()
    
    # 清理临时文件
    cleanup_temp_files()
    
    print()
    
    if success:
        print("🎉 AutoMem 已成功停止")
    else:
        print("⚠️ 部分进程可能未能停止，请检查任务管理器")
    
    # 最终验证
    time.sleep(2)
    remaining_processes = find_automem_processes()
    if remaining_processes:
        print(f"⚠️ 仍有 {len(remaining_processes)} 个进程在运行:")
        for proc in remaining_processes:
            try:
                print(f"  PID {proc.pid}: {proc.name()}")
            except:
                pass
    else:
        print("✅ 所有AutoMem进程已停止")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 停止脚本被中断")
    except Exception as e:
        print(f"\n❌ 停止脚本执行失败: {e}")
        import traceback
        traceback.print_exc()
