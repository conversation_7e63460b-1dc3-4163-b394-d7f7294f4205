# AutoMem - 记忆增强系统

AutoMem是一个本地化的记忆增强系统，专为扩展Augment Agent的记忆功能而设计。

## 功能特性

- **自动上下文收集**: 自动记录项目代码结构、文件变更、对话记录等
- **持久化存储**: 使用SQLite数据库确保数据持久性
- **智能检索**: 提供基于关键词和语义的记忆检索功能
- **增量更新**: 避免重复存储，支持增量数据更新
- **API接口**: 简洁的API设计，易于集成到现有工具链

## 系统架构

```
AutoMem/
├── src/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── memory_store.py      # 记忆存储核心模块
│   │   ├── context_collector.py # 上下文收集器
│   │   └── memory_manager.py    # 记忆管理器
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── database.py          # SQLite数据库操作
│   │   └── models.py            # 数据模型定义
│   ├── api/
│   │   ├── __init__.py
│   │   └── memory_api.py        # API接口
│   └── utils/
│       ├── __init__.py
│       ├── file_monitor.py      # 文件监控工具
│       └── text_processor.py    # 文本处理工具
├── config/
│   └── config.yaml              # 配置文件
├── tests/
│   ├── __init__.py
│   ├── test_memory_store.py
│   ├── test_context_collector.py
│   └── test_api.py
├── data/
│   └── memory.db                # SQLite数据库文件
├── requirements.txt
└── setup.py
```

## 数据模型

### 记忆条目 (Memory Entry)
- ID: 唯一标识符
- Type: 记忆类型 (code, conversation, task, file_change)
- Content: 记忆内容
- Context: 上下文信息
- Timestamp: 创建时间
- Tags: 标签
- Hash: 内容哈希值（用于去重）

### 项目上下文 (Project Context)
- Project Path: 项目路径
- File Structure: 文件结构快照
- Dependencies: 依赖关系
- Last Updated: 最后更新时间

## 快速开始

1. 安装依赖
```bash
pip install -r requirements.txt
```

2. 初始化系统
```python
from src.core.memory_manager import MemoryManager

manager = MemoryManager()
manager.initialize()
```

3. 添加记忆
```python
manager.add_memory(
    memory_type="code",
    content="实现了用户认证功能",
    context={"file": "auth.py", "function": "login"},
    tags=["authentication", "security"]
)
```

4. 检索记忆
```python
memories = manager.search_memories("用户认证")
```

## 配置说明

系统配置文件位于 `config/config.yaml`，包含以下配置项：
- 数据库路径
- 监控目录
- 记忆保留策略
- API端口设置

## 与Augment集成

AutoMem设计为与Augment Agent无缝集成，可以通过以下方式使用：
1. 作为独立服务运行
2. 作为Python模块导入
3. 通过REST API调用
