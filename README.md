# AutoMem - 记忆增强系统

AutoMem是一个本地化的记忆增强系统，专为扩展Augment Agent的记忆功能而设计。

## 功能特性

- **自动上下文收集**: 自动记录项目代码结构、文件变更、对话记录等
- **持久化存储**: 使用SQLite数据库确保数据持久性
- **智能检索**: 提供基于关键词和语义的记忆检索功能
- **增量更新**: 避免重复存储，支持增量数据更新
- **API接口**: 简洁的API设计，易于集成到现有工具链

## 系统架构

```
AutoMem/
├── src/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── memory_store.py      # 记忆存储核心模块
│   │   ├── context_collector.py # 上下文收集器
│   │   └── memory_manager.py    # 记忆管理器
│   ├── storage/
│   │   ├── __init__.py
│   │   ├── database.py          # SQLite数据库操作
│   │   └── models.py            # 数据模型定义
│   ├── api/
│   │   ├── __init__.py
│   │   └── memory_api.py        # API接口
│   └── utils/
│       ├── __init__.py
│       ├── file_monitor.py      # 文件监控工具
│       └── text_processor.py    # 文本处理工具
├── config/
│   └── config.yaml              # 配置文件
├── tests/
│   ├── __init__.py
│   ├── test_memory_store.py
│   ├── test_context_collector.py
│   └── test_api.py
├── data/
│   └── memory.db                # SQLite数据库文件
├── requirements.txt
└── setup.py
```

## 数据模型

### 记忆条目 (Memory Entry)
- ID: 唯一标识符
- Type: 记忆类型 (code, conversation, task, file_change)
- Content: 记忆内容
- Context: 上下文信息
- Timestamp: 创建时间
- Tags: 标签
- Hash: 内容哈希值（用于去重）

### 项目上下文 (Project Context)
- Project Path: 项目路径
- File Structure: 文件结构快照
- Dependencies: 依赖关系
- Last Updated: 最后更新时间

## 快速开始

### 1. 安装依赖

#### 方式一：使用安装脚本（推荐）
```bash
python install.py
```

#### 方式二：手动安装
```bash
pip install pyyaml watchdog python-dateutil requests
```

**注意**: 如果遇到依赖安装问题，请使用安装脚本，它会自动处理依赖检查和安装。

### 2. 初始化系统
```bash
python src/main.py init
```

### 3. 启动系统

#### 方式一：使用启动脚本（推荐）
```bash
# 启动完整系统（API + Web GUI + 监控）
python start_automem.py --mode full --open-browser

# 仅启动Web GUI
python start_automem.py --mode gui --open-browser

# 仅启动API服务器
python start_automem.py --mode api

# 启动MCP服务器（用于Augment集成）
python start_automem.py --mode mcp
```

#### 方式二：直接使用主程序
```bash
# 启动完整系统
python src/main.py --gui

# 启动API服务器
python src/main.py --api-only

# 启动MCP服务器
python src/main.py --mcp
```

### 4. 使用Web GUI
访问 http://localhost:8888 使用可视化界面管理记忆

### 5. 编程接口使用
```python
from src.core.memory_manager import MemoryManager

manager = MemoryManager()
manager.initialize()

# 添加记忆
manager.add_memory(
    memory_type="code",
    content="实现了用户认证功能",
    context={"file": "auth.py", "function": "login"},
    tags=["authentication", "security"]
)

# 检索记忆
memories = manager.search_memories("用户认证")
```

## 配置说明

系统配置文件位于 `config/config.yaml`，包含以下配置项：
- 数据库路径
- 监控目录
- 记忆保留策略
- API端口设置

## 与Augment集成

AutoMem通过MCP (Model Context Protocol) 协议与Augment Agent无缝集成：

### MCP集成配置

1. 将以下配置添加到您的Augment配置文件：
```json
{
  "mcpServers": {
    "automem": {
      "command": "python",
      "args": [
        "C:/Users/<USER>/Desktop/project/AutoMem/src/mcp/stdio_server.py"
      ],
      "cwd": "C:/Users/<USER>/Desktop/project/AutoMem",
      "env": {
        "PYTHONPATH": "C:/Users/<USER>/Desktop/project/AutoMem"
      }
    }
  }
}
```

2. 启动AutoMem MCP服务器：
```bash
python start_automem.py --mode mcp
```

### 可用工具

- `add_memory`: 添加记忆条目
- `search_memories`: 搜索记忆
- `add_conversation`: 记录对话
- `add_task`: 记录任务
- `analyze_file`: 分析文件
- `get_statistics`: 获取统计信息

详细使用说明请参考 `augment_integration/INTEGRATION_GUIDE.md`

### 其他集成方式
1. 作为独立服务运行
2. 作为Python模块导入
3. 通过REST API调用
