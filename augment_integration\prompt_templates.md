# AutoMem 提示词模板

## 基础使用模板

### 记录代码工作

```
我刚完成了 [功能描述]，请帮我使用 AutoMem 记录这个工作：

文件: [文件路径]
功能: [具体功能描述]
技术要点: [关键技术点]
相关标签: [相关标签，用逗号分隔]

请使用 add_memory 工具记录，类型设为 "code"。
```

### 记录问题解决

```
我解决了一个 [问题类型] 问题：

问题描述: [问题的具体描述]
解决方案: [详细的解决步骤]
根本原因: [问题的根本原因]
预防措施: [如何避免类似问题]

请使用 add_memory 工具记录，类型设为 "note"，添加相关标签。
```

### 记录讨论和决策

```
请记录我们关于 [主题] 的讨论：

讨论内容: [讨论的主要内容]
关键决策: [做出的重要决策]
后续行动: [需要采取的行动]
参与者: [参与讨论的人员]

使用 add_conversation 工具记录。
```

### 创建任务记录

```
请帮我创建一个新任务记录：

任务名称: [任务名称]
详细描述: [任务的详细描述]
预期目标: [期望达到的目标]
当前状态: [created/in_progress/completed]

使用 add_task 工具记录。
```

## 高级使用模板

### 项目分析和记录

```
请帮我分析当前项目并建立记忆库：

1. 使用 analyze_file 工具分析以下重要文件：
   - [文件1路径]
   - [文件2路径]
   - [文件3路径]

2. 使用 get_statistics 工具查看当前记忆系统状态

3. 总结分析结果并提供项目概览
```

### 工作回顾

```
请帮我回顾最近的工作：

1. 使用 get_recent_memories 工具获取最近 [天数] 天的记忆
2. 按类型分类显示：
   - 代码相关工作
   - 解决的问题
   - 完成的任务
   - 重要讨论

3. 总结主要成果和待办事项
```

### 知识搜索

```
我需要查找关于 [主题] 的历史记录：

1. 使用 search_memories 工具搜索关键词 "[关键词]"
2. 过滤条件：
   - 记忆类型: [code/conversation/task/note]
   - 标签: [相关标签]
   - 数量限制: [数量]

3. 整理搜索结果并提供相关建议
```

## 自动化工作流模板

### 代码提交前检查

```
在我提交代码前，请执行以下检查流程：

1. 使用 analyze_file 工具分析修改的文件
2. 记录本次修改的主要内容
3. 检查是否有相关的历史记录或注意事项
4. 提供代码提交建议

修改的文件: [文件列表]
```

### 每日工作总结

```
请帮我生成今日工作总结：

1. 使用 get_recent_memories 工具获取今天的记忆 (days=1)
2. 按以下格式整理：
   - 完成的主要工作
   - 解决的问题
   - 新增的任务
   - 重要的讨论和决策

3. 生成明日工作建议
```

### 项目知识库更新

```
请更新项目知识库：

1. 分析项目中的新文件或修改文件
2. 更新相关的记忆记录
3. 检查任务状态并更新
4. 生成项目当前状态报告

项目路径: [项目路径]
关注的文件类型: [.py, .js, .md 等]
```

## 特定场景模板

### Bug 修复记录

```
我修复了一个 Bug，请记录详细信息：

Bug 描述: [Bug 的具体表现]
影响范围: [受影响的功能或用户]
修复方法: [具体的修复步骤]
测试验证: [如何验证修复效果]
相关文件: [修改的文件列表]

请使用 add_memory 工具记录，类型为 "code"，标签包含 "bug_fix"。
```

### 功能设计记录

```
我设计了一个新功能，请记录设计方案：

功能名称: [功能名称]
业务需求: [业务背景和需求]
技术方案: [技术实现方案]
接口设计: [API 或接口设计]
数据结构: [相关的数据结构]
实现计划: [开发计划和时间安排]

使用 add_memory 工具记录，类型为 "note"，标签包含 "design", "feature"。
```

### 学习笔记记录

```
我学习了 [技术/概念]，请记录学习笔记：

学习主题: [具体的学习内容]
关键概念: [重要的概念和原理]
实践示例: [代码示例或实践案例]
应用场景: [在项目中的应用可能]
参考资料: [学习资源链接]

使用 add_memory 工具记录，类型为 "note"，标签包含 "learning", [相关技术标签]。
```

### 会议记录

```
请记录会议内容：

会议主题: [会议主题]
参与人员: [参与者列表]
讨论要点: [主要讨论内容]
决策事项: [会议决策]
行动项目: [后续需要执行的任务]
下次会议: [下次会议安排]

使用 add_conversation 工具记录，主题设为会议主题。
```

## 查询和分析模板

### 技术债务分析

```
请帮我分析项目中的技术债务：

1. 搜索包含 "TODO", "FIXME", "HACK" 等关键词的记忆
2. 查找标签为 "technical_debt" 的记录
3. 分析代码质量相关的历史记录
4. 生成技术债务清单和优先级建议

使用 search_memories 工具进行分析。
```

### 性能优化历史

```
我需要了解项目的性能优化历史：

1. 搜索关键词 "性能", "优化", "performance"
2. 过滤类型为 "code" 和 "note" 的记忆
3. 按时间顺序整理优化记录
4. 分析优化效果和经验教训

请提供详细的性能优化历史报告。
```

### 安全相关记录

```
请整理项目的安全相关记录：

1. 搜索标签包含 "security", "authentication", "authorization" 的记忆
2. 查找安全漏洞修复记录
3. 整理安全最佳实践
4. 生成安全检查清单

使用相关工具进行全面分析。
```

## 团队协作模板

### 知识传承

```
新团队成员加入，请准备知识传承材料：

1. 获取项目统计信息
2. 整理核心代码记录
3. 收集重要的设计决策
4. 准备常见问题解答
5. 生成新人入门指南

目标受众: [新成员背景]
重点领域: [需要重点了解的领域]
```

### 代码审查准备

```
准备代码审查材料：

1. 分析待审查的文件
2. 查找相关的历史记录和设计决策
3. 检查是否符合项目规范
4. 准备审查要点清单

待审查文件: [文件列表]
审查重点: [特别关注的方面]
```

### 项目交接

```
准备项目交接文档：

1. 生成完整的项目记忆导出
2. 整理关键决策和设计理念
3. 收集未完成的任务和已知问题
4. 准备运维和部署相关记录

交接范围: [交接的具体内容]
接收方: [接收项目的团队或个人]
```

## 使用提示

1. **具体化**: 提供具体的文件路径、功能描述和技术细节
2. **标签化**: 合理使用标签帮助后续搜索和分类
3. **结构化**: 按照模板格式组织信息，便于理解和处理
4. **及时性**: 在完成工作后及时记录，避免遗忘重要细节
5. **关联性**: 在记录时考虑与其他记忆的关联关系

这些模板可以根据具体项目和团队需求进行调整和扩展。
