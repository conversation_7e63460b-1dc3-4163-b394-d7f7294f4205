#!/usr/bin/env python3
"""
AutoMem 测试运行器
"""
import sys
import unittest
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.WARNING,  # 测试时减少日志输出
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("AutoMem 测试套件")
    print("=" * 60)
    
    # 设置日志
    setup_test_logging()
    
    # 发现并运行测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # 返回是否成功
    return len(result.failures) == 0 and len(result.errors) == 0

def run_specific_test(test_module):
    """运行特定测试模块"""
    print(f"运行测试模块: {test_module}")
    
    setup_test_logging()
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName(f'tests.{test_module}')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return len(result.failures) == 0 and len(result.errors) == 0

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行特定测试
        test_module = sys.argv[1]
        success = run_specific_test(test_module)
    else:
        # 运行所有测试
        success = run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
