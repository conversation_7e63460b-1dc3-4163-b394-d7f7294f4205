"""
AutoMem MCP (Model Context Protocol) 服务器实现
"""
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.core.memory_manager import MemoryManager


class MCPServer:
    """MCP服务器"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.logger = logging.getLogger(__name__)
        
        # MCP工具定义
        self.tools = {
            "add_memory": {
                "name": "add_memory",
                "description": "添加新的记忆条目到AutoMem系统",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "memory_type": {
                            "type": "string",
                            "description": "记忆类型",
                            "enum": ["code", "conversation", "task", "file_change", "note"]
                        },
                        "content": {
                            "type": "string",
                            "description": "记忆内容"
                        },
                        "context": {
                            "type": "object",
                            "description": "上下文信息",
                            "additionalProperties": True
                        },
                        "tags": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "标签列表"
                        }
                    },
                    "required": ["memory_type", "content"]
                }
            },
            "search_memories": {
                "name": "search_memories",
                "description": "搜索记忆条目",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索关键词"
                        },
                        "memory_types": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "记忆类型过滤"
                        },
                        "tags": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "标签过滤"
                        },
                        "limit": {
                            "type": "integer",
                            "description": "结果数量限制",
                            "default": 10
                        }
                    }
                }
            },
            "get_memory": {
                "name": "get_memory",
                "description": "获取特定记忆条目的详细信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "memory_id": {
                            "type": "string",
                            "description": "记忆ID"
                        }
                    },
                    "required": ["memory_id"]
                }
            },
            "get_statistics": {
                "name": "get_statistics",
                "description": "获取记忆系统统计信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {}
                }
            },
            "add_conversation": {
                "name": "add_conversation",
                "description": "添加对话记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "content": {
                            "type": "string",
                            "description": "对话内容"
                        },
                        "participant": {
                            "type": "string",
                            "description": "参与者",
                            "default": "user"
                        },
                        "topic": {
                            "type": "string",
                            "description": "对话主题"
                        }
                    },
                    "required": ["content"]
                }
            },
            "add_task": {
                "name": "add_task",
                "description": "添加任务记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "任务名称"
                        },
                        "description": {
                            "type": "string",
                            "description": "任务描述"
                        },
                        "state": {
                            "type": "string",
                            "description": "任务状态",
                            "enum": ["created", "in_progress", "completed", "cancelled"],
                            "default": "created"
                        }
                    },
                    "required": ["name", "description"]
                }
            },
            "analyze_file": {
                "name": "analyze_file",
                "description": "分析文件并添加到记忆系统",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            "get_recent_memories": {
                "name": "get_recent_memories",
                "description": "获取最近的记忆",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "days": {
                            "type": "integer",
                            "description": "天数",
                            "default": 7
                        },
                        "limit": {
                            "type": "integer",
                            "description": "数量限制",
                            "default": 20
                        }
                    }
                }
            }
        }
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        try:
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")
            
            if method == "initialize":
                return await self._handle_initialize(request_id, params)
            elif method == "tools/list":
                return await self._handle_list_tools(request_id)
            elif method == "tools/call":
                return await self._handle_call_tool(request_id, params)
            else:
                return self._create_error_response(request_id, -32601, f"Method not found: {method}")
                
        except Exception as e:
            self.logger.error(f"处理MCP请求失败: {e}")
            return self._create_error_response(request.get("id"), -32603, str(e))
    
    async def _handle_initialize(self, request_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理初始化请求"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": "AutoMem",
                    "version": "1.0.0",
                    "description": "AutoMem记忆增强系统MCP服务器"
                }
            }
        }
    
    async def _handle_list_tools(self, request_id: str) -> Dict[str, Any]:
        """处理工具列表请求"""
        tools_list = list(self.tools.values())
        
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "tools": tools_list
            }
        }
    
    async def _handle_call_tool(self, request_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理工具调用请求"""
        tool_name = params.get("name")
        arguments = params.get("arguments", {})
        
        if tool_name not in self.tools:
            return self._create_error_response(request_id, -32602, f"Unknown tool: {tool_name}")
        
        try:
            result = await self._execute_tool(tool_name, arguments)
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(result, ensure_ascii=True, indent=2)
                        }
                    ]
                }
            }
            
        except Exception as e:
            self.logger.error(f"执行工具 {tool_name} 失败: {e}")
            return self._create_error_response(request_id, -32603, f"Tool execution failed: {str(e)}")
    
    async def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具"""
        if tool_name == "add_memory":
            return await self._tool_add_memory(arguments)
        elif tool_name == "search_memories":
            return await self._tool_search_memories(arguments)
        elif tool_name == "get_memory":
            return await self._tool_get_memory(arguments)
        elif tool_name == "get_statistics":
            return await self._tool_get_statistics(arguments)
        elif tool_name == "add_conversation":
            return await self._tool_add_conversation(arguments)
        elif tool_name == "add_task":
            return await self._tool_add_task(arguments)
        elif tool_name == "analyze_file":
            return await self._tool_analyze_file(arguments)
        elif tool_name == "get_recent_memories":
            return await self._tool_get_recent_memories(arguments)
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
    
    async def _tool_add_memory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """添加记忆工具"""
        memory_id = self.memory_manager.add_memory(
            memory_type=args["memory_type"],
            content=args["content"],
            context=args.get("context", {}),
            tags=args.get("tags", [])
        )
        
        return {
            "success": True,
            "memory_id": memory_id,
            "message": "记忆添加成功"
        }
    
    async def _tool_search_memories(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """搜索记忆工具"""
        memories = self.memory_manager.search_memories(
            query=args.get("query", ""),
            memory_types=args.get("memory_types", []),
            tags=args.get("tags", []),
            limit=args.get("limit", 10)
        )
        
        return {
            "success": True,
            "memories": [memory.to_dict() for memory in memories],
            "count": len(memories)
        }
    
    async def _tool_get_memory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取记忆工具"""
        memory = self.memory_manager.get_memory(args["memory_id"])
        
        if memory:
            return {
                "success": True,
                "memory": memory.to_dict()
            }
        else:
            return {
                "success": False,
                "error": "记忆不存在"
            }
    
    async def _tool_get_statistics(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取统计信息工具"""
        stats = self.memory_manager.get_statistics()
        
        return {
            "success": True,
            "statistics": stats
        }
    
    async def _tool_add_conversation(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """添加对话记忆工具"""
        memory_id = self.memory_manager.add_conversation_memory(
            content=args["content"],
            participant=args.get("participant", "user"),
            topic=args.get("topic", "")
        )
        
        return {
            "success": True,
            "memory_id": memory_id,
            "message": "对话记忆添加成功"
        }
    
    async def _tool_add_task(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """添加任务记忆工具"""
        memory_id = self.memory_manager.add_task_memory(
            task_name=args["name"],
            description=args["description"],
            state=args.get("state", "created")
        )
        
        return {
            "success": True,
            "memory_id": memory_id,
            "message": "任务记忆添加成功"
        }
    
    async def _tool_analyze_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """分析文件工具"""
        memory = self.memory_manager.analyze_file(args["file_path"])
        
        if memory:
            return {
                "success": True,
                "memory_id": memory.id,
                "memory": memory.to_dict(),
                "message": "文件分析完成"
            }
        else:
            return {
                "success": False,
                "error": "文件分析失败"
            }
    
    async def _tool_get_recent_memories(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取最近记忆工具"""
        memories = self.memory_manager.get_recent_memories(
            days=args.get("days", 7),
            limit=args.get("limit", 20)
        )
        
        return {
            "success": True,
            "memories": [memory.to_dict() for memory in memories],
            "count": len(memories)
        }
    
    def _create_error_response(self, request_id: str, code: int, message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": code,
                "message": message
            }
        }
