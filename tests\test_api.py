"""
API接口测试
"""
import unittest
import tempfile
import json
import threading
import time
from pathlib import Path
import requests

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.memory_manager import MemoryManager
from src.api.memory_api import MemoryAPIServer


class TestMemoryAPI(unittest.TestCase):
    """记忆API测试"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.temp_dir = tempfile.mkdtemp()
        cls.project_root = Path(cls.temp_dir)
        
        # 创建测试配置
        config = {
            'database': {'path': str(cls.project_root / 'test_memory.db')},
            'memory': {'auto_tag': True, 'deduplication': True},
            'context': {'max_content_length': 1000}
        }
        
        # 保存配置文件
        config_file = cls.project_root / 'config.yaml'
        import yaml
        with open(config_file, 'w') as f:
            yaml.dump(config, f)
        
        # 初始化记忆管理器
        cls.memory_manager = MemoryManager(str(config_file), str(cls.project_root))
        
        # 启动API服务器
        cls.api_server = MemoryAPIServer(cls.memory_manager, 'localhost', 8081)
        cls.api_server.start()
        
        # 等待服务器启动
        time.sleep(1)
        
        cls.base_url = "http://localhost:8081"
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        cls.api_server.stop()
        cls.memory_manager.shutdown()
        
        import shutil
        shutil.rmtree(cls.temp_dir)
    
    def test_health_check(self):
        """测试健康检查"""
        response = requests.get(f"{self.base_url}/api/health")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 'healthy')
    
    def test_create_memory(self):
        """测试创建记忆"""
        memory_data = {
            'memory_type': 'test',
            'content': '测试记忆内容',
            'context': {'test': 'value'},
            'tags': ['test', 'api']
        }
        
        response = requests.post(
            f"{self.base_url}/api/memories",
            json=memory_data
        )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertIn('id', data)
        self.assertEqual(data['message'], 'Memory created successfully')
        
        return data['id']
    
    def test_get_memory(self):
        """测试获取单个记忆"""
        # 先创建一个记忆
        memory_id = self.test_create_memory()
        
        response = requests.get(f"{self.base_url}/api/memories/{memory_id}")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['id'], memory_id)
        self.assertEqual(data['content'], '测试记忆内容')
    
    def test_get_memories(self):
        """测试获取记忆列表"""
        # 创建几个测试记忆
        for i in range(3):
            memory_data = {
                'memory_type': 'test',
                'content': f'测试记忆{i}',
                'tags': ['test']
            }
            requests.post(f"{self.base_url}/api/memories", json=memory_data)
        
        response = requests.get(f"{self.base_url}/api/memories?limit=10")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('memories', data)
        self.assertGreaterEqual(len(data['memories']), 3)
    
    def test_search_memories(self):
        """测试搜索记忆"""
        # 创建测试记忆
        memory_data = {
            'memory_type': 'code',
            'content': '实现了用户认证功能',
            'tags': ['authentication', 'security']
        }
        requests.post(f"{self.base_url}/api/memories", json=memory_data)
        
        # 搜索记忆
        search_data = {
            'query': '用户认证',
            'memory_types': ['code'],
            'limit': 10
        }
        
        response = requests.post(
            f"{self.base_url}/api/memories/search",
            json=search_data
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('memories', data)
        self.assertGreater(len(data['memories']), 0)
        
        # 验证搜索结果
        found_memory = data['memories'][0]
        self.assertIn('用户认证', found_memory['content'])
    
    def test_update_memory(self):
        """测试更新记忆"""
        # 先创建一个记忆
        memory_id = self.test_create_memory()
        
        # 更新记忆
        update_data = {
            'content': '更新后的内容',
            'tags': ['updated', 'test']
        }
        
        response = requests.put(
            f"{self.base_url}/api/memories/{memory_id}",
            json=update_data
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['message'], 'Memory updated successfully')
        
        # 验证更新
        response = requests.get(f"{self.base_url}/api/memories/{memory_id}")
        data = response.json()
        self.assertEqual(data['content'], '更新后的内容')
        self.assertIn('updated', data['tags'])
    
    def test_delete_memory(self):
        """测试删除记忆"""
        # 先创建一个记忆
        memory_id = self.test_create_memory()
        
        # 删除记忆
        response = requests.delete(f"{self.base_url}/api/memories/{memory_id}")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['message'], 'Memory deleted successfully')
        
        # 验证删除
        response = requests.get(f"{self.base_url}/api/memories/{memory_id}")
        self.assertEqual(response.status_code, 404)
    
    def test_create_conversation(self):
        """测试创建对话记忆"""
        conversation_data = {
            'content': '讨论了数据库设计方案',
            'participant': 'user',
            'topic': '数据库设计'
        }
        
        response = requests.post(
            f"{self.base_url}/api/conversations",
            json=conversation_data
        )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertIn('id', data)
        self.assertEqual(data['message'], 'Conversation memory created successfully')
    
    def test_create_task(self):
        """测试创建任务记忆"""
        task_data = {
            'name': '实现API接口',
            'description': '创建REST API接口',
            'state': 'in_progress'
        }
        
        response = requests.post(
            f"{self.base_url}/api/tasks",
            json=task_data
        )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertIn('id', data)
        self.assertEqual(data['message'], 'Task memory created successfully')
    
    def test_get_statistics(self):
        """测试获取统计信息"""
        response = requests.get(f"{self.base_url}/api/statistics")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('total_entries', data)
        self.assertIn('type_statistics', data)
        self.assertIn('project_root', data)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的记忆ID
        response = requests.get(f"{self.base_url}/api/memories/invalid-id")
        self.assertEqual(response.status_code, 404)
        
        # 测试缺少必需字段
        response = requests.post(
            f"{self.base_url}/api/memories",
            json={'memory_type': 'test'}  # 缺少content
        )
        self.assertEqual(response.status_code, 400)
        
        # 测试不存在的端点
        response = requests.get(f"{self.base_url}/api/nonexistent")
        self.assertEqual(response.status_code, 404)
    
    def test_cors_headers(self):
        """测试CORS头"""
        response = requests.options(f"{self.base_url}/api/memories")
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers.get('Access-Control-Allow-Origin'), '*')
        self.assertIn('GET', response.headers.get('Access-Control-Allow-Methods', ''))


class TestMemoryAPIIntegration(unittest.TestCase):
    """记忆API集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = Path(self.temp_dir)
        
        # 创建测试文件
        test_file = self.project_root / "test.py"
        test_file.write_text('''
def hello():
    print("Hello, World!")
''')
        
        # 初始化记忆管理器
        self.memory_manager = MemoryManager(project_root=str(self.project_root))
        
        # 启动API服务器
        self.api_server = MemoryAPIServer(self.memory_manager, 'localhost', 8082)
        self.api_server.start()
        time.sleep(1)
        
        self.base_url = "http://localhost:8082"
    
    def tearDown(self):
        """测试后清理"""
        self.api_server.stop()
        self.memory_manager.shutdown()
        
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_analyze_file_api(self):
        """测试文件分析API"""
        analyze_data = {
            'file_path': str(self.project_root / "test.py")
        }
        
        response = requests.post(
            f"{self.base_url}/api/analyze",
            json=analyze_data
        )
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertIn('id', data)
        self.assertIn('memory', data)
        
        # 验证分析结果
        memory = data['memory']
        self.assertEqual(memory['memory_type'], 'code')
        self.assertEqual(memory['context']['language'], 'python')
    
    def test_export_memories_api(self):
        """测试导出记忆API"""
        # 先创建一些记忆
        memory_data = {
            'memory_type': 'test',
            'content': '测试导出功能',
            'tags': ['export', 'test']
        }
        requests.post(f"{self.base_url}/api/memories", json=memory_data)
        
        # 导出记忆
        export_data = {
            'output_path': str(self.project_root / 'export.json'),
            'memory_types': ['test']
        }
        
        response = requests.post(
            f"{self.base_url}/api/export",
            json=export_data
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['message'], 'Memories exported successfully')
        
        # 验证导出文件
        export_file = Path(export_data['output_path'])
        self.assertTrue(export_file.exists())
        
        with open(export_file, 'r', encoding='utf-8') as f:
            export_content = json.load(f)
        
        self.assertIn('memories', export_content)
        self.assertGreater(len(export_content['memories']), 0)


if __name__ == '__main__':
    unittest.main()
