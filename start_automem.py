#!/usr/bin/env python3
"""
AutoMem 启动脚本
提供多种启动模式的便捷入口
"""
import sys
import argparse
import subprocess
import webbrowser
import time
import socket
from pathlib import Path

def check_port(host, port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0  # 0表示端口被占用
    except:
        return False

def find_free_port(start_port, max_attempts=100):
    """找到可用的端口"""
    for port in range(start_port, start_port + max_attempts):
        if not check_port('localhost', port):
            return port
    return None

def main():
    parser = argparse.ArgumentParser(description="AutoMem 启动脚本")
    parser.add_argument("--mode", choices=["full", "api", "gui", "mcp", "demo"], 
                       default="full", help="启动模式")
    parser.add_argument("--host", default="localhost", help="服务器主机")
    parser.add_argument("--api-port", type=int, default=8080, help="API端口")
    parser.add_argument("--gui-port", type=int, default=8888, help="GUI端口")
    parser.add_argument("--open-browser", action="store_true", help="自动打开浏览器")
    
    args = parser.parse_args()

    # 检查端口是否被占用
    if args.mode in ["full", "api", "gui"]:
        if check_port(args.host, args.api_port):
            print(f"⚠️ API端口 {args.api_port} 被占用")
            new_api_port = find_free_port(args.api_port)
            if new_api_port:
                print(f"🔄 自动切换到端口 {new_api_port}")
                args.api_port = new_api_port
            else:
                print("❌ 无法找到可用的API端口")
                print("请运行: python check_ports.py")
                sys.exit(1)

        if args.mode in ["full", "gui"] and check_port(args.host, args.gui_port):
            print(f"⚠️ GUI端口 {args.gui_port} 被占用")
            new_gui_port = find_free_port(args.gui_port)
            if new_gui_port:
                print(f"🔄 自动切换到端口 {new_gui_port}")
                args.gui_port = new_gui_port
            else:
                print("❌ 无法找到可用的GUI端口")
                print("请运行: python check_ports.py")
                sys.exit(1)

    if args.mode == "full":
        print("🚀 启动完整的AutoMem系统（API + GUI + 监控）")
        cmd = [
            sys.executable, "src/main.py",
            "--gui",
            "--host", args.host,
            "--port", str(args.api_port),
            "--gui-port", str(args.gui_port)
        ]
        
        if args.open_browser:
            # 延迟打开浏览器
            import threading
            def open_browser():
                time.sleep(3)  # 等待服务器启动
                webbrowser.open(f"http://{args.host}:{args.gui_port}")
            
            threading.Thread(target=open_browser, daemon=True).start()
        
        subprocess.run(cmd)
        
    elif args.mode == "api":
        print("🔌 启动AutoMem API服务器")
        cmd = [
            sys.executable, "src/main.py",
            "--api-only",
            "--host", args.host,
            "--port", str(args.api_port)
        ]
        subprocess.run(cmd)
        
    elif args.mode == "gui":
        print("🖥️ 启动AutoMem Web GUI")
        cmd = [
            sys.executable, "src/main.py",
            "--gui",
            "--no-api",
            "--host", args.host,
            "--gui-port", str(args.gui_port)
        ]
        
        if args.open_browser:
            import threading
            def open_browser():
                time.sleep(3)
                webbrowser.open(f"http://{args.host}:{args.gui_port}")
            
            threading.Thread(target=open_browser, daemon=True).start()
        
        subprocess.run(cmd)
        
    elif args.mode == "mcp":
        print("🔗 启动AutoMem MCP服务器")
        cmd = [sys.executable, "src/main.py", "--mcp"]
        subprocess.run(cmd)
        
    elif args.mode == "demo":
        print("🎯 启动AutoMem演示")
        print("选择演示类型:")
        print("1. 基本功能演示")
        print("2. API服务器演示")
        print("3. 文件监控演示")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        demo_types = {"1": "basic", "2": "api", "3": "monitor"}
        demo_type = demo_types.get(choice, "basic")
        
        cmd = [sys.executable, "demo.py", demo_type]
        subprocess.run(cmd)
    
    else:
        print(f"❌ 未知的启动模式: {args.mode}")
        sys.exit(1)

if __name__ == "__main__":
    main()
