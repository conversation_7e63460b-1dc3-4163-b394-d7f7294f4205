# AutoMem 与 Augment Agent 集成指南

## 概述

AutoMem 通过 MCP (Model Context Protocol) 协议与 Augment Agent 进行集成，提供强大的本地记忆增强功能。

## 安装和配置

### 1. 安装 AutoMem

```bash
# 克隆或下载 AutoMem 项目
cd /path/to/AutoMem

# 安装依赖
pip install -r requirements.txt

# 初始化系统
python src/main.py init
```

### 2. 配置 MCP 服务器

将 `augment_integration/mcp_config.json` 文件内容添加到您的 Augment 配置中：

```json
{
  "mcpServers": {
    "automem": {
      "command": "python",
      "args": [
        "src/mcp/stdio_server.py"
      ],
      "cwd": "/path/to/AutoMem",
      "env": {
        "PYTHONPATH": "."
      }
    }
  }
}
```

**重要**: 请将 `"cwd"` 路径修改为您的 AutoMem 项目实际路径。

### 3. 验证连接

启动 Augment Agent 后，您应该能看到 AutoMem 工具可用。可以使用以下命令测试：

```
请使用 get_statistics 工具查看 AutoMem 的统计信息
```

## 可用工具

AutoMem 为 Augment Agent 提供以下工具：

### 1. add_memory
添加新的记忆条目

**参数**:
- `memory_type` (必需): 记忆类型 (code, conversation, task, file_change, note)
- `content` (必需): 记忆内容
- `context` (可选): 上下文信息对象
- `tags` (可选): 标签数组

**示例**:
```json
{
  "memory_type": "code",
  "content": "实现了用户认证功能，包括JWT令牌生成和验证",
  "context": {
    "file": "auth.py",
    "function": "authenticate_user",
    "line_number": 45
  },
  "tags": ["authentication", "security", "jwt"]
}
```

### 2. search_memories
搜索记忆条目

**参数**:
- `query` (可选): 搜索关键词
- `memory_types` (可选): 记忆类型过滤数组
- `tags` (可选): 标签过滤数组
- `limit` (可选): 结果数量限制，默认10

**示例**:
```json
{
  "query": "用户认证",
  "memory_types": ["code", "conversation"],
  "tags": ["authentication"],
  "limit": 5
}
```

### 3. get_memory
获取特定记忆条目的详细信息

**参数**:
- `memory_id` (必需): 记忆ID

### 4. add_conversation
添加对话记忆

**参数**:
- `content` (必需): 对话内容
- `participant` (可选): 参与者，默认"user"
- `topic` (可选): 对话主题

### 5. add_task
添加任务记忆

**参数**:
- `name` (必需): 任务名称
- `description` (必需): 任务描述
- `state` (可选): 任务状态，默认"created"

### 6. analyze_file
分析文件并添加到记忆系统

**参数**:
- `file_path` (必需): 文件路径

### 7. get_recent_memories
获取最近的记忆

**参数**:
- `days` (可选): 天数，默认7
- `limit` (可选): 数量限制，默认20

### 8. get_statistics
获取记忆系统统计信息

**参数**: 无

## 使用场景和提示词模板

### 场景1: 代码开发记录

当您完成代码开发时，可以使用以下提示词：

```
我刚完成了用户认证模块的开发，请帮我记录这个工作：
- 文件: src/auth.py
- 功能: 实现JWT认证和用户登录
- 关键点: 使用bcrypt加密密码，JWT过期时间设置为24小时

请使用 add_memory 工具记录这个代码记忆。
```

### 场景2: 问题解决记录

当您解决了一个技术问题时：

```
我刚解决了数据库连接超时的问题，解决方案是调整连接池配置。请帮我记录这个解决方案，以便将来遇到类似问题时可以快速找到。

请使用 add_memory 工具，类型设为 "note"，并添加相关标签。
```

### 场景3: 项目进展跟踪

记录项目任务和进展：

```
请帮我记录一个新任务：
- 任务名称: 实现用户权限管理
- 描述: 添加角色权限系统，支持管理员、普通用户等角色
- 状态: 计划中

使用 add_task 工具记录。
```

### 场景4: 查找历史信息

当需要查找之前的工作记录时：

```
我需要查找之前关于"数据库优化"的所有记录，请帮我搜索相关记忆。

使用 search_memories 工具，搜索关键词"数据库优化"。
```

### 场景5: 对话记录

记录重要的讨论和决策：

```
请记录我们刚才关于API设计的讨论：
- 内容: 决定使用RESTful API设计，采用JSON格式，添加版本控制
- 主题: API架构设计
- 参与者: 开发团队

使用 add_conversation 工具记录。
```

## 高级使用技巧

### 1. 自动记录工作流程

您可以要求 Augment Agent 在完成特定任务后自动记录：

```
每当我完成代码修改时，请自动使用 analyze_file 工具分析修改的文件，并记录到记忆系统中。
```

### 2. 定期回顾

设置定期回顾工作记录：

```
请使用 get_recent_memories 工具查看我最近7天的工作记录，并总结主要进展。
```

### 3. 项目知识库构建

逐步构建项目知识库：

```
请分析当前项目的所有Python文件，使用 analyze_file 工具为每个重要文件创建记忆记录，帮助构建项目知识库。
```

## 故障排除

### 1. MCP 连接失败

如果 Augment Agent 无法连接到 AutoMem：

1. 检查 AutoMem 路径配置是否正确
2. 确认 Python 环境和依赖已正确安装
3. 查看 `logs/mcp_server.log` 文件中的错误信息

### 2. 工具调用失败

如果工具调用返回错误：

1. 检查参数格式是否正确
2. 确认 AutoMem 数据库是否正常初始化
3. 查看日志文件获取详细错误信息

### 3. 性能问题

如果响应较慢：

1. 检查数据库大小，考虑清理旧记录
2. 调整搜索限制参数
3. 优化查询条件

## 最佳实践

### 1. 记忆分类

- **code**: 代码实现、算法、技术方案
- **conversation**: 讨论、决策、会议记录
- **task**: 任务规划、进度跟踪
- **note**: 学习笔记、问题解决方案
- **file_change**: 文件修改记录（自动生成）

### 2. 标签使用

使用有意义的标签帮助分类和搜索：
- 技术标签: `python`, `javascript`, `database`, `api`
- 功能标签: `authentication`, `ui`, `backend`, `frontend`
- 状态标签: `completed`, `in_progress`, `bug_fix`, `feature`

### 3. 内容描述

记忆内容应该：
- 简洁明了，突出关键信息
- 包含足够的上下文信息
- 使用一致的描述格式

### 4. 定期维护

- 定期清理过期记忆
- 更新任务状态
- 整理和优化标签系统

## 示例工作流程

以下是一个完整的开发工作流程示例：

```
1. 开始新功能开发
"我要开始开发用户注册功能，请帮我记录这个任务"
-> 使用 add_task 工具

2. 实现代码
"我完成了用户注册的后端API，请分析并记录这个文件"
-> 使用 analyze_file 工具

3. 遇到问题并解决
"我解决了邮箱验证的问题，请记录解决方案"
-> 使用 add_memory 工具

4. 完成功能
"用户注册功能已完成，请更新任务状态并记录完成情况"
-> 使用 add_task 工具更新状态

5. 回顾工作
"请查看我今天的工作记录"
-> 使用 get_recent_memories 工具
```

通过这种方式，AutoMem 将成为您的智能工作助手，帮助您构建完整的项目知识库和工作记录。
