#!/usr/bin/env python3
"""
AutoMem 服务器管理器 - GUI工具
类似MCP Feedback Collector的管理界面
"""
import sys
import os
import subprocess
import threading
import time
import webbrowser
from pathlib import Path
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import psutil

class AutoMemManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AutoMem 服务器管理器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 状态变量
        self.is_running = False
        self.current_process = None
        self.api_port = 8080
        self.gui_port = 8888
        self.current_mode = "完整模式"
        
        self.setup_ui()
        self.update_status()
        
        # 定期更新状态
        self.root.after(2000, self.periodic_update)
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="AutoMem 服务器管理器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 服务器状态区域
        status_frame = ttk.LabelFrame(main_frame, text="服务器状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="停止", foreground="red")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(status_frame, text="PID:").grid(row=1, column=0, sticky=tk.W)
        self.pid_label = ttk.Label(status_frame, text="-")
        self.pid_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 服务器控制区域
        control_frame = ttk.LabelFrame(main_frame, text="服务器控制", padding="10")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 启动模式选择
        ttk.Label(control_frame, text="启动模式:").grid(row=0, column=0, sticky=tk.W)
        
        self.mode_var = tk.StringVar(value="完整模式")
        mode_frame = ttk.Frame(control_frame)
        mode_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 10))
        
        ttk.Radiobutton(mode_frame, text="完整模式 (API + GUI)", 
                       variable=self.mode_var, value="完整模式").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="仅API服务器", 
                       variable=self.mode_var, value="API模式").grid(row=1, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="仅Web界面", 
                       variable=self.mode_var, value="GUI模式").grid(row=2, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="MCP服务器", 
                       variable=self.mode_var, value="MCP模式").grid(row=3, column=0, sticky=tk.W)
        
        # 端口设置
        port_frame = ttk.Frame(control_frame)
        port_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(port_frame, text="API端口:").grid(row=0, column=0, sticky=tk.W)
        self.api_port_var = tk.StringVar(value="8080")
        api_port_entry = ttk.Entry(port_frame, textvariable=self.api_port_var, width=8)
        api_port_entry.grid(row=0, column=1, padx=(5, 20))
        
        ttk.Label(port_frame, text="GUI端口:").grid(row=0, column=2, sticky=tk.W)
        self.gui_port_var = tk.StringVar(value="8888")
        gui_port_entry = ttk.Entry(port_frame, textvariable=self.gui_port_var, width=8)
        gui_port_entry.grid(row=0, column=3, padx=(5, 0))
        
        # 分离模式选项
        self.detached_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(control_frame, text="分离模式 (服务器独立运行，即使关闭管理器也不停止服务器)", 
                       variable=self.detached_var).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(5, 10))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        self.start_button = ttk.Button(button_frame, text="启动服务器", command=self.start_server)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止服务器", command=self.stop_server, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        self.restart_button = ttk.Button(button_frame, text="重启服务器", command=self.restart_server, state="disabled")
        self.restart_button.grid(row=0, column=2, padx=(0, 10))
        
        ttk.Button(button_frame, text="检查健康状态", command=self.check_health).grid(row=0, column=3, padx=(0, 10))
        
        ttk.Button(button_frame, text="显示配置", command=self.show_config).grid(row=0, column=4)
        
        # 快捷操作
        shortcut_frame = ttk.Frame(control_frame)
        shortcut_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(shortcut_frame, text="打开Web界面", command=self.open_web_gui).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(shortcut_frame, text="查看日志", command=self.view_logs).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(shortcut_frame, text="清空日志", command=self.clear_logs).grid(row=0, column=2)
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="日志输出", padding="5")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 底部按钮
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(bottom_frame, text="退出程序", command=self.quit_application).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(bottom_frame, text="退出但保持服务器运行", command=self.quit_keep_server).grid(row=0, column=1)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", f"{len(lines)-500}.0")
    
    def update_status(self):
        """更新服务器状态"""
        # 检查AutoMem进程
        automem_processes = self.find_automem_processes()
        
        if automem_processes:
            self.is_running = True
            self.status_label.config(text="运行中", foreground="green")
            
            # 显示第一个进程的PID
            if automem_processes:
                self.pid_label.config(text=str(automem_processes[0].pid))
            
            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.restart_button.config(state="normal")
        else:
            self.is_running = False
            self.status_label.config(text="停止", foreground="red")
            self.pid_label.config(text="-")
            
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.restart_button.config(state="disabled")
    
    def find_automem_processes(self):
        """查找AutoMem进程"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and any('automem' in str(arg).lower() or 
                                     'src/main.py' in str(arg) or
                                     'start_mcp_server.py' in str(arg) or
                                     'quick_start.py' in str(arg)
                                     for arg in cmdline):
                        processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.log_message(f"查找进程时出错: {e}")
        
        return processes

    def start_server(self):
        """启动服务器"""
        if self.is_running:
            messagebox.showwarning("警告", "服务器已在运行中")
            return

        try:
            mode = self.mode_var.get()
            api_port = self.api_port_var.get()
            gui_port = self.gui_port_var.get()

            self.log_message(f"启动AutoMem服务器 - 模式: {mode}")

            # 构建启动命令
            if mode == "完整模式":
                cmd = [sys.executable, "quick_start.py"]
            elif mode == "API模式":
                cmd = [sys.executable, "start_automem.py", "--mode", "api", "--api-port", api_port]
            elif mode == "GUI模式":
                cmd = [sys.executable, "start_automem.py", "--mode", "gui", "--gui-port", gui_port]
            elif mode == "MCP模式":
                cmd = [sys.executable, "start_mcp_server.py"]
            else:
                cmd = [sys.executable, "quick_start.py"]

            # 启动进程
            if self.detached_var.get():
                # 分离模式 - 进程独立运行
                if os.name == 'nt':  # Windows
                    self.current_process = subprocess.Popen(
                        cmd,
                        creationflags=subprocess.CREATE_NEW_CONSOLE,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                else:  # Unix/Linux
                    self.current_process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        start_new_session=True
                    )
                self.log_message(f"服务器已在分离模式下启动 (PID: {self.current_process.pid})")
            else:
                # 附加模式 - 进程跟随管理器
                self.current_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                self.log_message(f"服务器已启动 (PID: {self.current_process.pid})")

                # 启动日志读取线程
                threading.Thread(target=self.read_process_output, daemon=True).start()

            # 延迟更新状态
            self.root.after(2000, self.update_status)

        except Exception as e:
            self.log_message(f"启动服务器失败: {e}")
            messagebox.showerror("错误", f"启动服务器失败: {e}")

    def stop_server(self):
        """停止服务器"""
        try:
            self.log_message("正在停止AutoMem服务器...")

            # 运行停止脚本
            result = subprocess.run([sys.executable, "stop_automem.py"],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.log_message("服务器已成功停止")
            else:
                self.log_message(f"停止服务器时出现警告: {result.stderr}")

            self.current_process = None
            self.update_status()

        except Exception as e:
            self.log_message(f"停止服务器失败: {e}")
            messagebox.showerror("错误", f"停止服务器失败: {e}")

    def restart_server(self):
        """重启服务器"""
        self.log_message("重启服务器...")
        self.stop_server()
        time.sleep(3)
        self.start_server()

    def check_health(self):
        """检查服务器健康状态"""
        try:
            import requests

            api_port = self.api_port_var.get()
            gui_port = self.gui_port_var.get()

            health_status = []

            # 检查API服务器
            try:
                response = requests.get(f"http://localhost:{api_port}/api/health", timeout=5)
                if response.status_code == 200:
                    health_status.append(f"✅ API服务器 (端口 {api_port}): 正常")
                else:
                    health_status.append(f"⚠️ API服务器 (端口 {api_port}): 响应异常")
            except:
                health_status.append(f"❌ API服务器 (端口 {api_port}): 无响应")

            # 检查GUI服务器
            try:
                response = requests.get(f"http://localhost:{gui_port}/", timeout=5)
                if response.status_code == 200:
                    health_status.append(f"✅ GUI服务器 (端口 {gui_port}): 正常")
                else:
                    health_status.append(f"⚠️ GUI服务器 (端口 {gui_port}): 响应异常")
            except:
                health_status.append(f"❌ GUI服务器 (端口 {gui_port}): 无响应")

            status_message = "\n".join(health_status)
            self.log_message(f"健康检查结果:\n{status_message}")
            messagebox.showinfo("健康检查", status_message)

        except ImportError:
            messagebox.showwarning("警告", "需要安装requests库来进行健康检查")
        except Exception as e:
            self.log_message(f"健康检查失败: {e}")
            messagebox.showerror("错误", f"健康检查失败: {e}")

    def show_config(self):
        """显示配置信息"""
        config_info = f"""AutoMem 配置信息:

启动模式: {self.mode_var.get()}
API端口: {self.api_port_var.get()}
GUI端口: {self.gui_port_var.get()}
分离模式: {'是' if self.detached_var.get() else '否'}

项目路径: {Path.cwd()}
Python路径: {sys.executable}

可用脚本:
- quick_start.py (快速启动)
- start_automem.py (启动脚本)
- stop_automem.py (停止脚本)
- start_mcp_server.py (MCP服务器)
- automem_manager.py (GUI管理器)
"""

        # 创建配置窗口
        config_window = tk.Toplevel(self.root)
        config_window.title("配置信息")
        config_window.geometry("500x400")

        text_widget = scrolledtext.ScrolledText(config_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, config_info)
        text_widget.config(state=tk.DISABLED)

    def open_web_gui(self):
        """打开Web界面"""
        gui_port = self.gui_port_var.get()
        url = f"http://localhost:{gui_port}"

        try:
            webbrowser.open(url)
            self.log_message(f"已打开Web界面: {url}")
        except Exception as e:
            self.log_message(f"打开Web界面失败: {e}")
            messagebox.showerror("错误", f"打开Web界面失败: {e}")

    def view_logs(self):
        """查看日志文件"""
        log_dir = Path("logs")
        if log_dir.exists():
            if os.name == 'nt':  # Windows
                os.startfile(str(log_dir))
            else:  # Unix/Linux
                subprocess.run(["xdg-open", str(log_dir)])
        else:
            messagebox.showinfo("信息", "日志目录不存在")

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空日志吗？"):
            self.log_text.delete("1.0", tk.END)
            self.log_message("日志已清空")

    def read_process_output(self):
        """读取进程输出"""
        if self.current_process:
            try:
                for line in iter(self.current_process.stdout.readline, ''):
                    if line:
                        self.root.after(0, lambda l=line.strip(): self.log_message(l))
                    if self.current_process.poll() is not None:
                        break
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"读取进程输出失败: {e}"))

    def periodic_update(self):
        """定期更新状态"""
        self.update_status()
        self.root.after(5000, self.periodic_update)  # 每5秒更新一次

    def quit_application(self):
        """退出应用程序"""
        if self.is_running and not self.detached_var.get():
            if messagebox.askyesno("确认", "服务器正在运行，是否停止服务器并退出？"):
                self.stop_server()
                time.sleep(2)
                self.root.quit()
            else:
                return
        else:
            self.root.quit()

    def quit_keep_server(self):
        """退出但保持服务器运行"""
        if self.is_running:
            if messagebox.askyesno("确认", "确定要退出管理器但保持服务器运行吗？"):
                self.root.quit()
        else:
            self.root.quit()

    def run(self):
        """运行GUI"""
        self.log_message("AutoMem 服务器管理器已启动")
        self.root.mainloop()

def main():
    """主函数"""
    # 检查依赖
    try:
        import psutil
    except ImportError:
        print("错误: 需要安装psutil库")
        print("请运行: pip install psutil")
        sys.exit(1)

    # 启动GUI
    app = AutoMemManager()
    app.run()

if __name__ == "__main__":
    main()
