#!/usr/bin/env python3
"""
AutoMem 集成管理器 - 一站式管理工具
包含项目管理、记忆管理、服务器控制等所有功能
"""
import sys
import os
import subprocess
import threading
import time
import webbrowser
import json
from pathlib import Path
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import psutil

class AutoMemIntegratedManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AutoMem 集成管理器")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_running = False
        self.current_process = None
        self.current_project_path = str(Path.cwd())
        self.memory_manager = None
        
        # 配置变量
        self.api_port = tk.StringVar(value="8080")
        self.gui_port = tk.StringVar(value="8888")
        self.mode_var = tk.StringVar(value="完整模式")
        self.detached_var = tk.BooleanVar(value=False)
        
        self.setup_ui()
        self.load_recent_projects()
        self.update_status()
        
        # 定期更新状态
        self.root.after(3000, self.periodic_update)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建各个标签页
        self.create_project_tab()
        self.create_server_tab()
        self.create_memory_tab()
        self.create_logs_tab()
        self.create_settings_tab()
    
    def create_project_tab(self):
        """创建项目管理标签页"""
        project_frame = ttk.Frame(self.notebook)
        self.notebook.add(project_frame, text="项目管理")
        
        # 当前项目区域
        current_frame = ttk.LabelFrame(project_frame, text="当前项目", padding="10")
        current_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(current_frame, text="项目路径:").grid(row=0, column=0, sticky=tk.W)
        self.project_path_var = tk.StringVar(value=self.current_project_path)
        project_entry = ttk.Entry(current_frame, textvariable=self.project_path_var, width=60)
        project_entry.grid(row=0, column=1, padx=(10, 5), sticky=(tk.W, tk.E))
        
        ttk.Button(current_frame, text="浏览", command=self.browse_project).grid(row=0, column=2, padx=5)
        ttk.Button(current_frame, text="切换项目", command=self.switch_project).grid(row=0, column=3, padx=5)
        
        current_frame.columnconfigure(1, weight=1)
        
        # 项目信息
        info_frame = ttk.LabelFrame(project_frame, text="项目信息", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.project_info_text = scrolledtext.ScrolledText(info_frame, height=8)
        self.project_info_text.pack(fill=tk.BOTH, expand=True)
        
        # 最近项目
        recent_frame = ttk.LabelFrame(project_frame, text="最近项目", padding="10")
        recent_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建最近项目列表
        self.recent_projects_listbox = tk.Listbox(recent_frame, height=4)
        self.recent_projects_listbox.pack(fill=tk.X, pady=(0, 5))
        self.recent_projects_listbox.bind('<Double-Button-1>', self.load_recent_project)
        
        recent_buttons_frame = ttk.Frame(recent_frame)
        recent_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(recent_buttons_frame, text="加载选中项目", command=self.load_recent_project).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(recent_buttons_frame, text="移除选中项目", command=self.remove_recent_project).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(recent_buttons_frame, text="清空列表", command=self.clear_recent_projects).pack(side=tk.LEFT)
        
        # 项目操作
        action_frame = ttk.LabelFrame(project_frame, text="项目操作", padding="10")
        action_frame.pack(fill=tk.X, padx=10, pady=5)
        
        action_buttons_frame = ttk.Frame(action_frame)
        action_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(action_buttons_frame, text="分析项目", command=self.analyze_project).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_buttons_frame, text="刷新信息", command=self.refresh_project_info).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_buttons_frame, text="打开项目文件夹", command=self.open_project_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_buttons_frame, text="导出项目记忆", command=self.export_project_memories).pack(side=tk.LEFT)
    
    def create_server_tab(self):
        """创建服务器控制标签页"""
        server_frame = ttk.Frame(self.notebook)
        self.notebook.add(server_frame, text="服务器控制")
        
        # 服务器状态
        status_frame = ttk.LabelFrame(server_frame, text="服务器状态", padding="10")
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        status_grid = ttk.Frame(status_frame)
        status_grid.pack(fill=tk.X)
        
        ttk.Label(status_grid, text="状态:").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_grid, text="停止", foreground="red")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 20))
        
        ttk.Label(status_grid, text="PID:").grid(row=0, column=2, sticky=tk.W)
        self.pid_label = ttk.Label(status_grid, text="-")
        self.pid_label.grid(row=0, column=3, sticky=tk.W, padx=(10, 20))
        
        ttk.Label(status_grid, text="运行时间:").grid(row=0, column=4, sticky=tk.W)
        self.uptime_label = ttk.Label(status_grid, text="-")
        self.uptime_label.grid(row=0, column=5, sticky=tk.W, padx=(10, 0))
        
        # 服务器配置
        config_frame = ttk.LabelFrame(server_frame, text="服务器配置", padding="10")
        config_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 启动模式
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(mode_frame, text="启动模式:").pack(side=tk.LEFT)
        
        modes_frame = ttk.Frame(mode_frame)
        modes_frame.pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Radiobutton(modes_frame, text="完整模式", variable=self.mode_var, value="完整模式").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(modes_frame, text="API模式", variable=self.mode_var, value="API模式").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(modes_frame, text="GUI模式", variable=self.mode_var, value="GUI模式").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(modes_frame, text="MCP模式", variable=self.mode_var, value="MCP模式").pack(side=tk.LEFT)
        
        # 端口配置
        port_frame = ttk.Frame(config_frame)
        port_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(port_frame, text="API端口:").pack(side=tk.LEFT)
        ttk.Entry(port_frame, textvariable=self.api_port, width=8).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(port_frame, text="GUI端口:").pack(side=tk.LEFT)
        ttk.Entry(port_frame, textvariable=self.gui_port, width=8).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Checkbutton(port_frame, text="分离模式", variable=self.detached_var).pack(side=tk.LEFT, padx=(20, 0))
        
        # 控制按钮
        control_frame = ttk.LabelFrame(server_frame, text="服务器控制", padding="10")
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill=tk.X)
        
        self.start_button = ttk.Button(buttons_frame, text="启动服务器", command=self.start_server)
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(buttons_frame, text="停止服务器", command=self.stop_server, state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.restart_button = ttk.Button(buttons_frame, text="重启服务器", command=self.restart_server, state="disabled")
        self.restart_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(buttons_frame, text="健康检查", command=self.check_health).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="打开Web界面", command=self.open_web_gui).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="查看配置", command=self.show_config).pack(side=tk.LEFT)
        
        # 快速操作
        quick_frame = ttk.LabelFrame(server_frame, text="快速操作", padding="10")
        quick_frame.pack(fill=tk.X, padx=10, pady=5)
        
        quick_buttons_frame = ttk.Frame(quick_frame)
        quick_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(quick_buttons_frame, text="一键启动(自动端口)", command=self.quick_start).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons_frame, text="强制停止所有", command=self.force_stop_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons_frame, text="检查端口占用", command=self.check_ports).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(quick_buttons_frame, text="清理临时文件", command=self.cleanup_temp_files).pack(side=tk.LEFT)
    
    def create_memory_tab(self):
        """创建记忆管理标签页"""
        memory_frame = ttk.Frame(self.notebook)
        self.notebook.add(memory_frame, text="记忆管理")
        
        # 记忆统计
        stats_frame = ttk.LabelFrame(memory_frame, text="记忆统计", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=4, wrap=tk.WORD)
        self.stats_text.pack(fill=tk.X)
        
        # 记忆搜索
        search_frame = ttk.LabelFrame(memory_frame, text="记忆搜索", padding="10")
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        search_input_frame = ttk.Frame(search_frame)
        search_input_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(search_input_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_input_frame, textvariable=self.search_var, width=40)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<Return>', lambda e: self.search_memories())
        
        ttk.Button(search_input_frame, text="搜索", command=self.search_memories).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_input_frame, text="清空", command=self.clear_search).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_input_frame, text="获取最近记忆", command=self.get_recent_memories).pack(side=tk.LEFT)
        
        # 记忆列表
        list_frame = ttk.LabelFrame(memory_frame, text="记忆列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建树形视图
        columns = ('类型', '时间', '内容预览')
        self.memory_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.memory_tree.heading(col, text=col)
            self.memory_tree.column(col, width=150)
        
        # 添加滚动条
        memory_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.memory_tree.yview)
        self.memory_tree.configure(yscrollcommand=memory_scrollbar.set)
        
        self.memory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        memory_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.memory_tree.bind('<Double-1>', self.view_memory_details)
        
        # 记忆操作
        memory_actions_frame = ttk.LabelFrame(memory_frame, text="记忆操作", padding="10")
        memory_actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        memory_buttons_frame = ttk.Frame(memory_actions_frame)
        memory_buttons_frame.pack(fill=tk.X)
        
        ttk.Button(memory_buttons_frame, text="查看详情", command=self.view_memory_details).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(memory_buttons_frame, text="添加记忆", command=self.add_memory_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(memory_buttons_frame, text="导出记忆", command=self.export_memories).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(memory_buttons_frame, text="清理过期记忆", command=self.cleanup_old_memories).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(memory_buttons_frame, text="刷新统计", command=self.refresh_memory_stats).pack(side=tk.LEFT)
    
    def create_logs_tab(self):
        """创建日志标签页"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="日志")
        
        # 日志控制
        log_control_frame = ttk.Frame(logs_frame)
        log_control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(log_control_frame, text="清空日志", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_control_frame, text="保存日志", command=self.save_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_control_frame, text="打开日志文件夹", command=self.open_log_folder).pack(side=tk.LEFT, padx=(0, 5))
        
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_control_frame, text="自动滚动", variable=self.auto_scroll_var).pack(side=tk.RIGHT)
        
        # 日志显示
        log_display_frame = ttk.LabelFrame(logs_frame, text="日志输出", padding="5")
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_display_frame, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)
    
    def create_settings_tab(self):
        """创建设置标签页"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="设置")
        
        # 应用设置
        app_settings_frame = ttk.LabelFrame(settings_frame, text="应用设置", padding="10")
        app_settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 自动启动设置
        self.auto_start_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(app_settings_frame, text="启动管理器时自动启动服务器", variable=self.auto_start_var).pack(anchor=tk.W)
        
        self.auto_open_browser_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(app_settings_frame, text="启动服务器时自动打开浏览器", variable=self.auto_open_browser_var).pack(anchor=tk.W)
        
        self.minimize_to_tray_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(app_settings_frame, text="最小化到系统托盘", variable=self.minimize_to_tray_var).pack(anchor=tk.W)
        
        # 记忆设置
        memory_settings_frame = ttk.LabelFrame(settings_frame, text="记忆设置", padding="10")
        memory_settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(memory_settings_frame, text="记忆保留天数:").grid(row=0, column=0, sticky=tk.W)
        self.retention_days_var = tk.StringVar(value="30")
        ttk.Entry(memory_settings_frame, textvariable=self.retention_days_var, width=10).grid(row=0, column=1, padx=(5, 0), sticky=tk.W)
        
        ttk.Label(memory_settings_frame, text="最大记忆条数:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.max_memories_var = tk.StringVar(value="10000")
        ttk.Entry(memory_settings_frame, textvariable=self.max_memories_var, width=10).grid(row=1, column=1, padx=(5, 0), sticky=tk.W, pady=(5, 0))
        
        # 监控设置
        monitor_settings_frame = ttk.LabelFrame(settings_frame, text="文件监控设置", padding="10")
        monitor_settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(monitor_settings_frame, text="监控文件类型:").pack(anchor=tk.W)
        self.file_patterns_var = tk.StringVar(value="*.py,*.js,*.md,*.txt,*.json,*.yaml")
        ttk.Entry(monitor_settings_frame, textvariable=self.file_patterns_var, width=60).pack(fill=tk.X, pady=(5, 10))
        
        ttk.Label(monitor_settings_frame, text="忽略目录:").pack(anchor=tk.W)
        self.ignore_patterns_var = tk.StringVar(value="__pycache__,.git,node_modules,.vscode")
        ttk.Entry(monitor_settings_frame, textvariable=self.ignore_patterns_var, width=60).pack(fill=tk.X, pady=(5, 10))
        
        # 设置操作
        settings_actions_frame = ttk.Frame(settings_frame)
        settings_actions_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(settings_actions_frame, text="保存设置", command=self.save_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(settings_actions_frame, text="重置设置", command=self.reset_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(settings_actions_frame, text="导入设置", command=self.import_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(settings_actions_frame, text="导出设置", command=self.export_settings).pack(side=tk.LEFT)
        
        # 关于信息
        about_frame = ttk.LabelFrame(settings_frame, text="关于", padding="10")
        about_frame.pack(fill=tk.X, padx=10, pady=5)
        
        about_text = """AutoMem 集成管理器 v2.0
一站式项目记忆管理解决方案

功能特性:
• 多项目动态切换
• 智能记忆管理
• 服务器控制
• 实时日志监控
• 灵活配置管理

开发: AutoMem Team
"""
        about_label = ttk.Label(about_frame, text=about_text, justify=tk.LEFT)
        about_label.pack(anchor=tk.W)

    # ==================== 项目管理功能 ====================

    def browse_project(self):
        """浏览选择项目目录"""
        directory = filedialog.askdirectory(
            title="选择项目目录",
            initialdir=self.current_project_path
        )
        if directory:
            self.project_path_var.set(directory)

    def switch_project(self):
        """切换项目"""
        new_path = self.project_path_var.get()
        if not Path(new_path).exists():
            messagebox.showerror("错误", "项目路径不存在")
            return

        if self.is_running:
            if messagebox.askyesno("确认", "服务器正在运行，是否停止服务器并切换项目？"):
                self.stop_server()
                time.sleep(2)
            else:
                return

        self.current_project_path = new_path
        self.save_recent_project(new_path)
        self.refresh_project_info()
        self.log_message(f"已切换到项目: {new_path}")
        messagebox.showinfo("成功", f"已切换到项目:\n{new_path}")

    def refresh_project_info(self):
        """刷新项目信息"""
        try:
            project_path = Path(self.current_project_path)

            # 收集项目信息
            info_lines = []
            info_lines.append(f"项目路径: {project_path}")
            info_lines.append(f"项目名称: {project_path.name}")

            # 统计文件信息
            file_count = 0
            dir_count = 0
            file_types = {}

            for item in project_path.rglob('*'):
                if item.is_file():
                    file_count += 1
                    suffix = item.suffix.lower()
                    file_types[suffix] = file_types.get(suffix, 0) + 1
                elif item.is_dir():
                    dir_count += 1

            info_lines.append(f"文件数量: {file_count}")
            info_lines.append(f"目录数量: {dir_count}")

            # 显示主要文件类型
            if file_types:
                info_lines.append("\n主要文件类型:")
                sorted_types = sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]
                for suffix, count in sorted_types:
                    suffix_name = suffix if suffix else "无扩展名"
                    info_lines.append(f"  {suffix_name}: {count} 个")

            # 检查是否有常见的项目文件
            project_files = {
                'package.json': 'Node.js项目',
                'requirements.txt': 'Python项目',
                'pom.xml': 'Maven项目',
                'Cargo.toml': 'Rust项目',
                'go.mod': 'Go项目',
                '.gitignore': 'Git仓库'
            }

            detected_types = []
            for file_name, project_type in project_files.items():
                if (project_path / file_name).exists():
                    detected_types.append(project_type)

            if detected_types:
                info_lines.append(f"\n检测到的项目类型: {', '.join(detected_types)}")

            # 更新显示
            self.project_info_text.delete(1.0, tk.END)
            self.project_info_text.insert(tk.END, '\n'.join(info_lines))

        except Exception as e:
            self.log_message(f"刷新项目信息失败: {e}")

    def analyze_project(self):
        """分析项目并生成记忆"""
        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        try:
            self.log_message("开始分析项目...")

            # 这里可以调用AutoMem的分析功能
            # 由于需要集成记忆管理器，我们先显示一个进度对话框
            progress_window = tk.Toplevel(self.root)
            progress_window.title("分析项目")
            progress_window.geometry("400x150")
            progress_window.transient(self.root)
            progress_window.grab_set()

            ttk.Label(progress_window, text="正在分析项目文件...").pack(pady=20)

            progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill=tk.X)
            progress_bar.start()

            # 在后台线程中执行分析
            def analyze_thread():
                try:
                    # 模拟分析过程
                    time.sleep(3)

                    # 这里应该调用实际的分析功能
                    self.root.after(0, lambda: self.finish_analysis(progress_window))

                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"项目分析失败: {e}"))
                    self.root.after(0, lambda: progress_window.destroy())

            threading.Thread(target=analyze_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"启动项目分析失败: {e}")
            messagebox.showerror("错误", f"启动项目分析失败: {e}")

    def finish_analysis(self, progress_window):
        """完成分析"""
        progress_window.destroy()
        self.log_message("项目分析完成")
        messagebox.showinfo("完成", "项目分析完成！")
        self.refresh_memory_stats()

    def open_project_folder(self):
        """打开项目文件夹"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(self.current_project_path)
            else:  # Unix/Linux
                subprocess.run(['xdg-open', self.current_project_path])
        except Exception as e:
            self.log_message(f"打开项目文件夹失败: {e}")

    def export_project_memories(self):
        """导出项目记忆"""
        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出项目记忆",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 这里应该调用实际的导出功能
                self.log_message(f"导出项目记忆到: {file_path}")
                messagebox.showinfo("成功", f"项目记忆已导出到:\n{file_path}")
            except Exception as e:
                self.log_message(f"导出项目记忆失败: {e}")
                messagebox.showerror("错误", f"导出失败: {e}")

    def load_recent_projects(self):
        """加载最近项目列表"""
        try:
            config_file = Path("automem_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    recent_projects = config.get('recent_projects', [])

                    self.recent_projects_listbox.delete(0, tk.END)
                    for project in recent_projects:
                        self.recent_projects_listbox.insert(tk.END, project)
        except Exception as e:
            self.log_message(f"加载最近项目失败: {e}")

    def save_recent_project(self, project_path):
        """保存最近项目"""
        try:
            config_file = Path("automem_config.json")
            config = {}

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            recent_projects = config.get('recent_projects', [])

            # 移除重复项
            if project_path in recent_projects:
                recent_projects.remove(project_path)

            # 添加到开头
            recent_projects.insert(0, project_path)

            # 限制数量
            recent_projects = recent_projects[:10]

            config['recent_projects'] = recent_projects

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 更新列表显示
            self.load_recent_projects()

        except Exception as e:
            self.log_message(f"保存最近项目失败: {e}")

    def load_recent_project(self, event=None):
        """加载选中的最近项目"""
        selection = self.recent_projects_listbox.curselection()
        if selection:
            project_path = self.recent_projects_listbox.get(selection[0])
            self.project_path_var.set(project_path)
            self.switch_project()

    def remove_recent_project(self):
        """移除选中的最近项目"""
        selection = self.recent_projects_listbox.curselection()
        if selection:
            if messagebox.askyesno("确认", "确定要从最近项目列表中移除选中项目吗？"):
                self.recent_projects_listbox.delete(selection[0])
                self.save_recent_projects_list()

    def clear_recent_projects(self):
        """清空最近项目列表"""
        if messagebox.askyesno("确认", "确定要清空最近项目列表吗？"):
            self.recent_projects_listbox.delete(0, tk.END)
            self.save_recent_projects_list()

    def save_recent_projects_list(self):
        """保存当前的最近项目列表"""
        try:
            config_file = Path("automem_config.json")
            config = {}

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            recent_projects = []
            for i in range(self.recent_projects_listbox.size()):
                recent_projects.append(self.recent_projects_listbox.get(i))

            config['recent_projects'] = recent_projects

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"保存最近项目列表失败: {e}")

    # ==================== 服务器控制功能 ====================

    def find_automem_processes(self):
        """查找AutoMem进程"""
        processes = []
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    cmdline = proc.info['cmdline']
                    if cmdline and any('automem' in str(arg).lower() or
                                     'src/main.py' in str(arg) or
                                     'start_mcp_server.py' in str(arg) or
                                     'quick_start.py' in str(arg)
                                     for arg in cmdline):
                        processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.log_message(f"查找进程时出错: {e}")

        return processes

    def update_status(self):
        """更新服务器状态"""
        processes = self.find_automem_processes()

        if processes:
            self.is_running = True
            self.status_label.config(text="运行中", foreground="green")

            # 显示第一个进程的信息
            proc = processes[0]
            self.pid_label.config(text=str(proc.pid))

            # 计算运行时间
            try:
                create_time = proc.create_time()
                uptime = time.time() - create_time
                uptime_str = self.format_uptime(uptime)
                self.uptime_label.config(text=uptime_str)
            except:
                self.uptime_label.config(text="未知")

            self.start_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.restart_button.config(state="normal")
        else:
            self.is_running = False
            self.status_label.config(text="停止", foreground="red")
            self.pid_label.config(text="-")
            self.uptime_label.config(text="-")

            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.restart_button.config(state="disabled")

    def format_uptime(self, seconds):
        """格式化运行时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def start_server(self):
        """启动服务器"""
        if self.is_running:
            messagebox.showwarning("警告", "服务器已在运行中")
            return

        try:
            mode = self.mode_var.get()
            api_port = self.api_port.get()
            gui_port = self.gui_port.get()

            self.log_message(f"启动AutoMem服务器 - 模式: {mode}, 项目: {self.current_project_path}")

            # 构建启动命令
            if mode == "完整模式":
                cmd = [sys.executable, "quick_start.py"]
            elif mode == "API模式":
                cmd = [sys.executable, "start_automem.py", "--mode", "api", "--api-port", api_port]
            elif mode == "GUI模式":
                cmd = [sys.executable, "start_automem.py", "--mode", "gui", "--gui-port", gui_port]
            elif mode == "MCP模式":
                cmd = [sys.executable, "start_mcp_server.py"]
            else:
                cmd = [sys.executable, "quick_start.py"]

            # 设置工作目录为当前项目路径
            cwd = self.current_project_path

            # 启动进程
            if self.detached_var.get():
                # 分离模式
                if os.name == 'nt':  # Windows
                    self.current_process = subprocess.Popen(
                        cmd,
                        cwd=cwd,
                        creationflags=subprocess.CREATE_NEW_CONSOLE,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                else:  # Unix/Linux
                    self.current_process = subprocess.Popen(
                        cmd,
                        cwd=cwd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        start_new_session=True
                    )
                self.log_message(f"服务器已在分离模式下启动 (PID: {self.current_process.pid})")
            else:
                # 附加模式
                self.current_process = subprocess.Popen(
                    cmd,
                    cwd=cwd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                self.log_message(f"服务器已启动 (PID: {self.current_process.pid})")

                # 启动日志读取线程
                threading.Thread(target=self.read_process_output, daemon=True).start()

            # 延迟更新状态
            self.root.after(3000, self.update_status)

            # 自动打开浏览器
            if self.auto_open_browser_var.get() and mode in ["完整模式", "GUI模式"]:
                self.root.after(5000, self.open_web_gui)

        except Exception as e:
            self.log_message(f"启动服务器失败: {e}")
            messagebox.showerror("错误", f"启动服务器失败: {e}")

    def stop_server(self):
        """停止服务器"""
        try:
            self.log_message("正在停止AutoMem服务器...")

            # 运行停止脚本
            result = subprocess.run([sys.executable, "stop_automem.py"],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.log_message("服务器已成功停止")
            else:
                self.log_message(f"停止服务器时出现警告: {result.stderr}")

            self.current_process = None
            self.update_status()

        except Exception as e:
            self.log_message(f"停止服务器失败: {e}")
            messagebox.showerror("错误", f"停止服务器失败: {e}")

    def restart_server(self):
        """重启服务器"""
        self.log_message("重启服务器...")
        self.stop_server()
        time.sleep(3)
        self.start_server()

    def quick_start(self):
        """一键启动（自动端口）"""
        # 自动检测可用端口
        api_port = self.find_free_port(8080)
        gui_port = self.find_free_port(8888)

        if api_port:
            self.api_port.set(str(api_port))
        if gui_port:
            self.gui_port.set(str(gui_port))

        self.mode_var.set("完整模式")
        self.start_server()

    def force_stop_all(self):
        """强制停止所有AutoMem进程"""
        if messagebox.askyesno("确认", "确定要强制停止所有AutoMem进程吗？"):
            processes = self.find_automem_processes()
            for proc in processes:
                try:
                    proc.kill()
                    self.log_message(f"强制终止进程 {proc.pid}")
                except:
                    pass

            self.update_status()
            self.log_message("已强制停止所有AutoMem进程")

    def find_free_port(self, start_port, max_attempts=100):
        """查找可用端口"""
        import socket
        for port in range(start_port, start_port + max_attempts):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                sock.close()
                if result != 0:  # 端口可用
                    return port
            except:
                continue
        return None

    def check_ports(self):
        """检查端口占用"""
        api_port = int(self.api_port.get())
        gui_port = int(self.gui_port.get())

        port_status = []

        for port in [api_port, gui_port]:
            if self.is_port_in_use(port):
                port_status.append(f"端口 {port}: 被占用")
            else:
                port_status.append(f"端口 {port}: 可用")

        status_message = "\n".join(port_status)
        messagebox.showinfo("端口状态", status_message)
        self.log_message(f"端口检查结果:\n{status_message}")

    def is_port_in_use(self, port):
        """检查端口是否被占用"""
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result == 0
        except:
            return False

    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_files = [
            "data/test.db",
            "data/test_fix.db",
            "test_monitoring.py"
        ]

        cleaned = 0
        for file_path in temp_files:
            try:
                path = Path(file_path)
                if path.exists():
                    path.unlink()
                    cleaned += 1
                    self.log_message(f"删除临时文件: {file_path}")
            except Exception as e:
                self.log_message(f"无法删除 {file_path}: {e}")

        messagebox.showinfo("清理完成", f"已清理 {cleaned} 个临时文件")

    def check_health(self):
        """检查服务器健康状态"""
        try:
            import requests

            api_port = self.api_port.get()
            gui_port = self.gui_port.get()

            health_status = []

            # 检查API服务器
            try:
                response = requests.get(f"http://localhost:{api_port}/api/health", timeout=5)
                if response.status_code == 200:
                    health_status.append(f"✅ API服务器 (端口 {api_port}): 正常")
                else:
                    health_status.append(f"⚠️ API服务器 (端口 {api_port}): 响应异常")
            except:
                health_status.append(f"❌ API服务器 (端口 {api_port}): 无响应")

            # 检查GUI服务器
            try:
                response = requests.get(f"http://localhost:{gui_port}/", timeout=5)
                if response.status_code == 200:
                    health_status.append(f"✅ GUI服务器 (端口 {gui_port}): 正常")
                else:
                    health_status.append(f"⚠️ GUI服务器 (端口 {gui_port}): 响应异常")
            except:
                health_status.append(f"❌ GUI服务器 (端口 {gui_port}): 无响应")

            status_message = "\n".join(health_status)
            self.log_message(f"健康检查结果:\n{status_message}")
            messagebox.showinfo("健康检查", status_message)

        except ImportError:
            messagebox.showwarning("警告", "需要安装requests库来进行健康检查")
        except Exception as e:
            self.log_message(f"健康检查失败: {e}")
            messagebox.showerror("错误", f"健康检查失败: {e}")

    def open_web_gui(self):
        """打开Web界面"""
        gui_port = self.gui_port.get()
        url = f"http://localhost:{gui_port}"

        try:
            webbrowser.open(url)
            self.log_message(f"已打开Web界面: {url}")
        except Exception as e:
            self.log_message(f"打开Web界面失败: {e}")
            messagebox.showerror("错误", f"打开Web界面失败: {e}")

    def show_config(self):
        """显示配置信息"""
        config_info = f"""AutoMem 配置信息:

当前项目: {self.current_project_path}
启动模式: {self.mode_var.get()}
API端口: {self.api_port.get()}
GUI端口: {self.gui_port.get()}
分离模式: {'是' if self.detached_var.get() else '否'}

Python路径: {sys.executable}
工作目录: {os.getcwd()}

服务器状态: {'运行中' if self.is_running else '停止'}
"""

        # 创建配置窗口
        config_window = tk.Toplevel(self.root)
        config_window.title("配置信息")
        config_window.geometry("500x400")
        config_window.transient(self.root)

        text_widget = scrolledtext.ScrolledText(config_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, config_info)
        text_widget.config(state=tk.DISABLED)

    def read_process_output(self):
        """读取进程输出"""
        if self.current_process:
            try:
                for line in iter(self.current_process.stdout.readline, ''):
                    if line:
                        self.root.after(0, lambda l=line.strip(): self.log_message(l))
                    if self.current_process.poll() is not None:
                        break
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"读取进程输出失败: {e}"))

    # ==================== 记忆管理功能 ====================

    def is_memory_manager_available(self):
        """检查记忆管理器是否可用"""
        return self.is_running

    def refresh_memory_stats(self):
        """刷新记忆统计"""
        if not self.is_memory_manager_available():
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "记忆管理器不可用，请先启动服务器")
            return

        try:
            # 这里应该调用AutoMem的统计API
            # 暂时显示模拟数据
            stats_info = """记忆统计信息:
总记忆数: 156
代码记忆: 89
对话记忆: 34
任务记忆: 23
文件变更: 10

最近活动: 2分钟前
监控状态: 活跃
项目: """ + Path(self.current_project_path).name

            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, stats_info)

        except Exception as e:
            self.log_message(f"刷新记忆统计失败: {e}")

    def search_memories(self):
        """搜索记忆"""
        query = self.search_var.get().strip()
        if not query:
            messagebox.showwarning("警告", "请输入搜索关键词")
            return

        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        try:
            self.log_message(f"搜索记忆: {query}")

            # 清空当前列表
            for item in self.memory_tree.get_children():
                self.memory_tree.delete(item)

            # 这里应该调用实际的搜索API
            # 暂时显示模拟数据
            sample_memories = [
                ("代码", "2025-08-04 10:30", f"实现了{query}相关功能"),
                ("对话", "2025-08-04 09:15", f"讨论了{query}的设计方案"),
                ("任务", "2025-08-04 08:45", f"完成{query}模块开发"),
            ]

            for memory_type, timestamp, content in sample_memories:
                self.memory_tree.insert('', tk.END, values=(memory_type, timestamp, content))

            self.log_message(f"找到 {len(sample_memories)} 条相关记忆")

        except Exception as e:
            self.log_message(f"搜索记忆失败: {e}")
            messagebox.showerror("错误", f"搜索失败: {e}")

    def clear_search(self):
        """清空搜索"""
        self.search_var.set("")
        for item in self.memory_tree.get_children():
            self.memory_tree.delete(item)

    def get_recent_memories(self):
        """获取最近记忆"""
        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        try:
            self.log_message("获取最近记忆...")

            # 清空当前列表
            for item in self.memory_tree.get_children():
                self.memory_tree.delete(item)

            # 这里应该调用实际的API
            # 暂时显示模拟数据
            recent_memories = [
                ("代码", "2025-08-04 11:20", "修复了编码问题"),
                ("对话", "2025-08-04 11:15", "用户反馈GUI需要改进"),
                ("文件变更", "2025-08-04 11:10", "更新了automem_manager.py"),
                ("任务", "2025-08-04 11:05", "完成集成管理器开发"),
                ("代码", "2025-08-04 11:00", "添加了项目切换功能"),
            ]

            for memory_type, timestamp, content in recent_memories:
                self.memory_tree.insert('', tk.END, values=(memory_type, timestamp, content))

            self.log_message(f"获取到 {len(recent_memories)} 条最近记忆")

        except Exception as e:
            self.log_message(f"获取最近记忆失败: {e}")
            messagebox.showerror("错误", f"获取最近记忆失败: {e}")

    def view_memory_details(self, event=None):
        """查看记忆详情"""
        selection = self.memory_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一条记忆")
            return

        item = self.memory_tree.item(selection[0])
        values = item['values']

        if not values:
            return

        # 创建详情窗口
        detail_window = tk.Toplevel(self.root)
        detail_window.title("记忆详情")
        detail_window.geometry("600x400")
        detail_window.transient(self.root)

        # 显示详情
        detail_text = scrolledtext.ScrolledText(detail_window, wrap=tk.WORD)
        detail_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        detail_info = f"""记忆类型: {values[0]}
创建时间: {values[1]}
内容: {values[2]}

详细信息:
这里应该显示完整的记忆内容、上下文信息、标签等详细数据。
当前显示的是模拟数据，实际使用时会从AutoMem API获取真实数据。
"""

        detail_text.insert(tk.END, detail_info)
        detail_text.config(state=tk.DISABLED)

    def add_memory_dialog(self):
        """添加记忆对话框"""
        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        # 创建添加记忆窗口
        add_window = tk.Toplevel(self.root)
        add_window.title("添加记忆")
        add_window.geometry("500x400")
        add_window.transient(self.root)
        add_window.grab_set()

        # 记忆类型
        ttk.Label(add_window, text="记忆类型:").pack(anchor=tk.W, padx=10, pady=(10, 5))
        memory_type_var = tk.StringVar(value="note")
        type_frame = ttk.Frame(add_window)
        type_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        for mem_type in ["code", "conversation", "task", "note", "file_change"]:
            ttk.Radiobutton(type_frame, text=mem_type, variable=memory_type_var, value=mem_type).pack(side=tk.LEFT, padx=(0, 10))

        # 内容
        ttk.Label(add_window, text="内容:").pack(anchor=tk.W, padx=10, pady=(0, 5))
        content_text = scrolledtext.ScrolledText(add_window, height=8)
        content_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 标签
        ttk.Label(add_window, text="标签 (用逗号分隔):").pack(anchor=tk.W, padx=10, pady=(0, 5))
        tags_var = tk.StringVar()
        ttk.Entry(add_window, textvariable=tags_var).pack(fill=tk.X, padx=10, pady=(0, 10))

        # 按钮
        button_frame = ttk.Frame(add_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def save_memory():
            memory_type = memory_type_var.get()
            content = content_text.get(1.0, tk.END).strip()
            tags = [tag.strip() for tag in tags_var.get().split(',') if tag.strip()]

            if not content:
                messagebox.showwarning("警告", "请输入记忆内容")
                return

            try:
                # 这里应该调用实际的添加记忆API
                self.log_message(f"添加记忆: {memory_type} - {content[:50]}...")
                messagebox.showinfo("成功", "记忆已添加")
                add_window.destroy()
                self.refresh_memory_stats()

            except Exception as e:
                self.log_message(f"添加记忆失败: {e}")
                messagebox.showerror("错误", f"添加记忆失败: {e}")

        ttk.Button(button_frame, text="保存", command=save_memory).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=add_window.destroy).pack(side=tk.LEFT)

    def export_memories(self):
        """导出记忆"""
        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出记忆",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 这里应该调用实际的导出API
                self.log_message(f"导出记忆到: {file_path}")
                messagebox.showinfo("成功", f"记忆已导出到:\n{file_path}")
            except Exception as e:
                self.log_message(f"导出记忆失败: {e}")
                messagebox.showerror("错误", f"导出失败: {e}")

    def cleanup_old_memories(self):
        """清理过期记忆"""
        if not self.is_memory_manager_available():
            messagebox.showwarning("警告", "记忆管理器不可用，请先启动服务器")
            return

        days = self.retention_days_var.get()
        try:
            days = int(days)
        except ValueError:
            messagebox.showerror("错误", "保留天数必须是数字")
            return

        if messagebox.askyesno("确认", f"确定要清理 {days} 天前的记忆吗？"):
            try:
                # 这里应该调用实际的清理API
                self.log_message(f"清理 {days} 天前的记忆...")
                messagebox.showinfo("完成", f"已清理过期记忆")
                self.refresh_memory_stats()
            except Exception as e:
                self.log_message(f"清理记忆失败: {e}")
                messagebox.showerror("错误", f"清理失败: {e}")

    # ==================== 日志和设置功能 ====================

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)

        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)

        # 限制日志长度
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", f"{len(lines)-500}.0")

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空日志吗？"):
            self.log_text.delete("1.0", tk.END)
            self.log_message("日志已清空")

    def save_logs(self):
        """保存日志"""
        file_path = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".log",
            filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get("1.0", tk.END))
                messagebox.showinfo("成功", f"日志已保存到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")

    def open_log_folder(self):
        """打开日志文件夹"""
        log_dir = Path("logs")
        if log_dir.exists():
            try:
                if os.name == 'nt':  # Windows
                    os.startfile(str(log_dir))
                else:  # Unix/Linux
                    subprocess.run(["xdg-open", str(log_dir)])
            except Exception as e:
                self.log_message(f"打开日志文件夹失败: {e}")
        else:
            messagebox.showinfo("信息", "日志目录不存在")

    def save_settings(self):
        """保存设置"""
        try:
            config = {
                'auto_start': self.auto_start_var.get(),
                'auto_open_browser': self.auto_open_browser_var.get(),
                'minimize_to_tray': self.minimize_to_tray_var.get(),
                'retention_days': self.retention_days_var.get(),
                'max_memories': self.max_memories_var.get(),
                'file_patterns': self.file_patterns_var.get(),
                'ignore_patterns': self.ignore_patterns_var.get(),
                'api_port': self.api_port.get(),
                'gui_port': self.gui_port.get(),
                'current_project': self.current_project_path
            }

            config_file = Path("automem_config.json")
            existing_config = {}

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)

            existing_config.update(config)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", "设置已保存")
            self.log_message("设置已保存")

        except Exception as e:
            self.log_message(f"保存设置失败: {e}")
            messagebox.showerror("错误", f"保存设置失败: {e}")

    def load_settings(self):
        """加载设置"""
        try:
            config_file = Path("automem_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                self.auto_start_var.set(config.get('auto_start', False))
                self.auto_open_browser_var.set(config.get('auto_open_browser', True))
                self.minimize_to_tray_var.set(config.get('minimize_to_tray', False))
                self.retention_days_var.set(config.get('retention_days', '30'))
                self.max_memories_var.set(config.get('max_memories', '10000'))
                self.file_patterns_var.set(config.get('file_patterns', '*.py,*.js,*.md,*.txt,*.json,*.yaml'))
                self.ignore_patterns_var.set(config.get('ignore_patterns', '__pycache__,.git,node_modules,.vscode'))
                self.api_port.set(config.get('api_port', '8080'))
                self.gui_port.set(config.get('gui_port', '8888'))

                if 'current_project' in config:
                    self.current_project_path = config['current_project']
                    self.project_path_var.set(self.current_project_path)

        except Exception as e:
            self.log_message(f"加载设置失败: {e}")

    def reset_settings(self):
        """重置设置"""
        if messagebox.askyesno("确认", "确定要重置所有设置到默认值吗？"):
            self.auto_start_var.set(False)
            self.auto_open_browser_var.set(True)
            self.minimize_to_tray_var.set(False)
            self.retention_days_var.set('30')
            self.max_memories_var.set('10000')
            self.file_patterns_var.set('*.py,*.js,*.md,*.txt,*.json,*.yaml')
            self.ignore_patterns_var.set('__pycache__,.git,node_modules,.vscode')
            self.api_port.set('8080')
            self.gui_port.set('8888')

            messagebox.showinfo("完成", "设置已重置")

    def import_settings(self):
        """导入设置"""
        file_path = filedialog.askopenfilename(
            title="导入设置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 应用设置
                for key, value in config.items():
                    if hasattr(self, f"{key}_var"):
                        getattr(self, f"{key}_var").set(value)

                messagebox.showinfo("成功", "设置已导入")
                self.log_message(f"从 {file_path} 导入设置")

            except Exception as e:
                messagebox.showerror("错误", f"导入设置失败: {e}")

    def export_settings(self):
        """导出设置"""
        file_path = filedialog.asksaveasfilename(
            title="导出设置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                config = {
                    'auto_start': self.auto_start_var.get(),
                    'auto_open_browser': self.auto_open_browser_var.get(),
                    'minimize_to_tray': self.minimize_to_tray_var.get(),
                    'retention_days': self.retention_days_var.get(),
                    'max_memories': self.max_memories_var.get(),
                    'file_patterns': self.file_patterns_var.get(),
                    'ignore_patterns': self.ignore_patterns_var.get(),
                    'api_port': self.api_port.get(),
                    'gui_port': self.gui_port.get()
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("成功", f"设置已导出到:\n{file_path}")
                self.log_message(f"设置已导出到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"导出设置失败: {e}")

    def periodic_update(self):
        """定期更新"""
        self.update_status()
        if hasattr(self, 'memory_tree') and self.memory_tree.winfo_exists():
            # 如果记忆管理标签页是活跃的，刷新统计
            if self.notebook.index(self.notebook.select()) == 2:  # 记忆管理是第3个标签页
                self.refresh_memory_stats()

        self.root.after(5000, self.periodic_update)  # 每5秒更新一次

    def on_closing(self):
        """关闭程序时的处理"""
        if self.is_running and not self.detached_var.get():
            if messagebox.askyesno("确认", "服务器正在运行，是否停止服务器并退出？"):
                self.stop_server()
                time.sleep(2)
                self.save_settings()
                self.root.destroy()
            else:
                return
        else:
            self.save_settings()
            self.root.destroy()

    def run(self):
        """运行GUI"""
        # 加载设置
        self.load_settings()

        # 刷新项目信息
        self.refresh_project_info()

        # 如果设置了自动启动
        if self.auto_start_var.get():
            self.root.after(2000, self.quick_start)

        self.log_message("AutoMem 集成管理器已启动")

        # 设置关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动GUI主循环
        self.root.mainloop()

def main():
    """主函数"""
    # 检查依赖
    missing_deps = []

    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")

    try:
        import psutil
    except ImportError:
        missing_deps.append("psutil")

    if missing_deps:
        print("错误: 缺少以下依赖:")
        for dep in missing_deps:
            print(f"  - {dep}")

        if 'psutil' in missing_deps:
            print("\n请运行: pip install psutil")

        if 'tkinter' in missing_deps:
            print("tkinter是Python标准库的一部分，如果缺失请重新安装Python")

        sys.exit(1)

    # 启动GUI
    try:
        app = AutoMemIntegratedManager()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
