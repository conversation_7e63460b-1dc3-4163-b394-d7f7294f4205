"""
记忆存储模块测试
"""
import unittest
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.storage.database import DatabaseManager
from src.storage.models import MemoryEntry, ProjectContext, SearchQuery


class TestDatabaseManager(unittest.TestCase):
    """数据库管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test_memory.db")
        self.db = DatabaseManager(self.db_path)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_add_memory_entry(self):
        """测试添加记忆条目"""
        memory = MemoryEntry(
            memory_type="test",
            content="测试内容",
            context={"test": "value"},
            tags=["test", "unit"],
            project_path="/test/project"
        )
        
        memory_id = self.db.add_memory_entry(memory)
        self.assertIsNotNone(memory_id)
        
        # 验证记忆已保存
        retrieved = self.db.get_memory_entry(memory_id)
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.content, "测试内容")
        self.assertEqual(retrieved.memory_type, "test")
    
    def test_search_memory_entries(self):
        """测试搜索记忆条目"""
        # 添加测试数据
        memories = [
            MemoryEntry(memory_type="code", content="Python函数定义", tags=["python"]),
            MemoryEntry(memory_type="conversation", content="讨论API设计", tags=["api"]),
            MemoryEntry(memory_type="code", content="JavaScript函数", tags=["javascript"])
        ]
        
        for memory in memories:
            self.db.add_memory_entry(memory)
        
        # 测试关键词搜索
        query = SearchQuery(keywords=["函数"])
        results = self.db.search_memory_entries(query)
        self.assertEqual(len(results), 2)
        
        # 测试类型过滤
        query = SearchQuery(memory_types=["code"])
        results = self.db.search_memory_entries(query)
        self.assertEqual(len(results), 2)
        
        # 测试标签过滤
        query = SearchQuery(tags=["python"])
        results = self.db.search_memory_entries(query)
        self.assertEqual(len(results), 1)
    
    def test_update_memory_entry(self):
        """测试更新记忆条目"""
        memory = MemoryEntry(
            memory_type="test",
            content="原始内容",
            project_path="/test"
        )
        
        memory_id = self.db.add_memory_entry(memory)
        
        # 更新内容
        memory.content = "更新后的内容"
        memory.tags = ["updated"]
        
        success = self.db.update_memory_entry(memory)
        self.assertTrue(success)
        
        # 验证更新
        retrieved = self.db.get_memory_entry(memory_id)
        self.assertEqual(retrieved.content, "更新后的内容")
        self.assertIn("updated", retrieved.tags)
    
    def test_delete_memory_entry(self):
        """测试删除记忆条目"""
        memory = MemoryEntry(
            memory_type="test",
            content="待删除内容",
            project_path="/test"
        )
        
        memory_id = self.db.add_memory_entry(memory)
        
        # 删除记忆
        success = self.db.delete_memory_entry(memory_id)
        self.assertTrue(success)
        
        # 验证删除
        retrieved = self.db.get_memory_entry(memory_id)
        self.assertIsNone(retrieved)
    
    def test_project_context(self):
        """测试项目上下文操作"""
        context = ProjectContext(
            project_path="/test/project",
            file_structure={"src": {"type": "directory"}},
            dependencies=["pytest", "requests"],
            metadata={"language": "python"}
        )
        
        context_id = self.db.add_project_context(context)
        self.assertIsNotNone(context_id)
        
        # 获取上下文
        retrieved = self.db.get_project_context("/test/project")
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.project_path, "/test/project")
        self.assertIn("pytest", retrieved.dependencies)
    
    def test_cleanup_old_entries(self):
        """测试清理过期条目"""
        # 添加新记忆
        new_memory = MemoryEntry(
            memory_type="test",
            content="新记忆",
            project_path="/test"
        )
        self.db.add_memory_entry(new_memory)
        
        # 添加旧记忆（手动设置时间戳）
        old_memory = MemoryEntry(
            memory_type="test",
            content="旧记忆",
            timestamp=datetime.now() - timedelta(days=35),
            project_path="/test"
        )
        self.db.add_memory_entry(old_memory)
        
        # 清理30天前的记忆
        deleted_count = self.db.cleanup_old_entries(30)
        self.assertEqual(deleted_count, 1)
        
        # 验证只有新记忆保留
        query = SearchQuery()
        remaining = self.db.search_memory_entries(query)
        self.assertEqual(len(remaining), 1)
        self.assertEqual(remaining[0].content, "新记忆")
    
    def test_statistics(self):
        """测试统计信息"""
        # 添加测试数据
        memories = [
            MemoryEntry(memory_type="code", content="代码1"),
            MemoryEntry(memory_type="code", content="代码2"),
            MemoryEntry(memory_type="conversation", content="对话1")
        ]
        
        for memory in memories:
            self.db.add_memory_entry(memory)
        
        stats = self.db.get_statistics()
        
        self.assertEqual(stats['total_entries'], 3)
        self.assertEqual(stats['type_statistics']['code'], 2)
        self.assertEqual(stats['type_statistics']['conversation'], 1)
    
    def test_deduplication(self):
        """测试去重功能"""
        memory1 = MemoryEntry(
            memory_type="test",
            content="重复内容",
            context={"key": "value"},
            project_path="/test"
        )
        
        memory2 = MemoryEntry(
            memory_type="test",
            content="重复内容",
            context={"key": "value"},
            project_path="/test"
        )
        
        # 添加相同内容的记忆
        id1 = self.db.add_memory_entry(memory1)
        id2 = self.db.add_memory_entry(memory2)
        
        # 应该返回相同的ID（去重）
        self.assertEqual(id1, id2)
        
        # 数据库中应该只有一条记录
        query = SearchQuery()
        results = self.db.search_memory_entries(query)
        self.assertEqual(len(results), 1)


class TestMemoryEntry(unittest.TestCase):
    """记忆条目模型测试"""
    
    def test_hash_generation(self):
        """测试哈希值生成"""
        memory1 = MemoryEntry(
            memory_type="test",
            content="测试内容",
            context={"key": "value"}
        )
        
        memory2 = MemoryEntry(
            memory_type="test",
            content="测试内容",
            context={"key": "value"}
        )
        
        # 相同内容应该生成相同哈希
        self.assertEqual(memory1.content_hash, memory2.content_hash)
        
        # 不同内容应该生成不同哈希
        memory3 = MemoryEntry(
            memory_type="test",
            content="不同内容",
            context={"key": "value"}
        )
        
        self.assertNotEqual(memory1.content_hash, memory3.content_hash)
    
    def test_serialization(self):
        """测试序列化和反序列化"""
        memory = MemoryEntry(
            memory_type="test",
            content="测试内容",
            context={"key": "value"},
            tags=["test", "unit"],
            project_path="/test/project"
        )
        
        # 转换为字典
        data = memory.to_dict()
        self.assertIsInstance(data, dict)
        self.assertEqual(data['memory_type'], "test")
        
        # 从字典恢复
        restored = MemoryEntry.from_dict(data)
        self.assertEqual(restored.memory_type, memory.memory_type)
        self.assertEqual(restored.content, memory.content)
        self.assertEqual(restored.context, memory.context)
        self.assertEqual(restored.tags, memory.tags)


if __name__ == '__main__':
    unittest.main()
