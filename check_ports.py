#!/usr/bin/env python3
"""
端口检查和修复脚本
"""
import socket
import subprocess
import sys

def check_port(host, port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0  # 0表示端口被占用
    except:
        return False

def find_free_port(start_port=8080, max_attempts=100):
    """找到可用的端口"""
    for port in range(start_port, start_port + max_attempts):
        if not check_port('localhost', port):
            return port
    return None

def kill_process_on_port(port):
    """尝试终止占用端口的进程（Windows）"""
    try:
        # 查找占用端口的进程
        result = subprocess.run(
            ['netstat', '-ano'], 
            capture_output=True, 
            text=True
        )
        
        lines = result.stdout.split('\n')
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    print(f"发现进程 {pid} 占用端口 {port}")
                    
                    # 询问是否终止进程
                    response = input(f"是否终止进程 {pid}? (y/n): ").lower().strip()
                    if response in ['y', 'yes', '是']:
                        subprocess.run(['taskkill', '/F', '/PID', pid])
                        print(f"已终止进程 {pid}")
                        return True
        
        return False
        
    except Exception as e:
        print(f"无法终止进程: {e}")
        return False

def main():
    """主函数"""
    print("🔍 AutoMem 端口检查工具")
    print("=" * 40)
    
    # 检查默认端口
    default_ports = {
        'API服务器': 8080,
        'GUI服务器': 8888
    }
    
    occupied_ports = []
    
    for service, port in default_ports.items():
        if check_port('localhost', port):
            print(f"❌ {service} 端口 {port} 被占用")
            occupied_ports.append((service, port))
        else:
            print(f"✅ {service} 端口 {port} 可用")
    
    if occupied_ports:
        print(f"\n⚠️ 发现 {len(occupied_ports)} 个端口被占用")
        
        # 提供解决方案
        print("\n解决方案:")
        print("1. 使用不同的端口启动")
        print("2. 终止占用端口的进程")
        print("3. 查看详细的端口使用情况")
        
        choice = input("\n请选择解决方案 (1-3): ").strip()
        
        if choice == '1':
            # 找到可用端口
            api_port = find_free_port(8080)
            gui_port = find_free_port(8888)
            
            if api_port and gui_port:
                print(f"\n🎯 建议使用以下命令启动:")
                print(f"python start_automem.py --mode full --api-port {api_port} --gui-port {gui_port} --open-browser")
                
                # 询问是否立即启动
                response = input("\n是否立即使用新端口启动? (y/n): ").lower().strip()
                if response in ['y', 'yes', '是']:
                    import subprocess
                    cmd = [
                        sys.executable, "start_automem.py", 
                        "--mode", "full",
                        "--api-port", str(api_port),
                        "--gui-port", str(gui_port),
                        "--open-browser"
                    ]
                    subprocess.run(cmd)
            else:
                print("❌ 无法找到可用端口")
        
        elif choice == '2':
            # 尝试终止进程
            for service, port in occupied_ports:
                print(f"\n处理 {service} (端口 {port}):")
                kill_process_on_port(port)
        
        elif choice == '3':
            # 显示详细信息
            print("\n📊 详细端口信息:")
            try:
                result = subprocess.run(
                    ['netstat', '-ano'], 
                    capture_output=True, 
                    text=True
                )
                
                lines = result.stdout.split('\n')
                for line in lines:
                    if ':8080' in line or ':8888' in line:
                        print(line.strip())
                        
            except Exception as e:
                print(f"无法获取端口信息: {e}")
    
    else:
        print("\n🎉 所有端口都可用！")
        print("可以正常启动AutoMem系统:")
        print("python start_automem.py --mode full --open-browser")

if __name__ == "__main__":
    main()
