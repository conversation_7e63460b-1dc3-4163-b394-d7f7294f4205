#!/usr/bin/env python3
"""
AutoMem 快速启动脚本
自动处理端口冲突并启动系统
"""
import sys
import socket
import subprocess
import webbrowser
import time
import threading

def check_port(host, port):
    """检查端口是否被占用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def find_free_port(start_port, max_attempts=100):
    """找到可用的端口"""
    for port in range(start_port, start_port + max_attempts):
        if not check_port('localhost', port):
            return port
    return None

def main():
    """主函数"""
    print("🚀 AutoMem 快速启动")
    print("=" * 30)
    
    # 查找可用端口
    api_port = 8080
    gui_port = 8888
    
    if check_port('localhost', api_port):
        print(f"⚠️ 端口 {api_port} 被占用，寻找替代端口...")
        api_port = find_free_port(8080)
        if api_port:
            print(f"✅ 使用API端口: {api_port}")
        else:
            print("❌ 无法找到可用的API端口")
            return
    
    if check_port('localhost', gui_port):
        print(f"⚠️ 端口 {gui_port} 被占用，寻找替代端口...")
        gui_port = find_free_port(8888)
        if gui_port:
            print(f"✅ 使用GUI端口: {gui_port}")
        else:
            print("❌ 无法找到可用的GUI端口")
            return
    
    print(f"🌐 API服务器: http://localhost:{api_port}")
    print(f"🖥️ Web界面: http://localhost:{gui_port}")
    print()
    
    # 启动系统
    cmd = [
        sys.executable, "src/main.py",
        "--gui",
        "--host", "localhost",
        "--port", str(api_port),
        "--gui-port", str(gui_port)
    ]
    
    print("🔄 启动AutoMem系统...")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(5)  # 等待服务器启动
        webbrowser.open(f"http://localhost:{gui_port}")
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 AutoMem已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n故障排除:")
        print("1. 运行: python diagnose_issue.py")
        print("2. 运行: python fix_common_issues.py")
        print("3. 检查: python check_ports.py")

if __name__ == "__main__":
    main()
