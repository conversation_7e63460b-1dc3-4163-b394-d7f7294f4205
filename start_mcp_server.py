#!/usr/bin/env python3
"""
AutoMem MCP服务器启动脚本
专门用于启动MCP服务器，解决路径问题
"""
import sys
import os
from pathlib import Path

def main():
    """启动MCP服务器"""
    # 获取当前脚本的绝对路径
    current_dir = Path(__file__).parent.absolute()
    
    # 设置项目根目录
    project_root = current_dir
    
    # 添加项目根目录到Python路径
    sys.path.insert(0, str(project_root))
    
    # 设置工作目录
    os.chdir(str(project_root))
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(project_root)
    
    print(f"🔗 启动AutoMem MCP服务器")
    print(f"📁 项目根目录: {project_root}")
    print(f"🐍 Python路径: {sys.executable}")
    print(f"📂 工作目录: {os.getcwd()}")
    print("=" * 50)
    
    try:
        # 导入并启动MCP服务器
        from src.mcp.stdio_server import STDIOServer
        import asyncio
        
        server = STDIOServer()
        asyncio.run(server.run())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖: python install.py")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ MCP服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
