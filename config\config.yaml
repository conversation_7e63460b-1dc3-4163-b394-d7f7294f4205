# AutoMem 配置文件

# 数据库配置
database:
  path: "data/memory.db"
  backup_interval: 3600  # 备份间隔（秒）
  max_backup_files: 5

# 项目监控配置
monitoring:
  project_root: "."
  watch_patterns:
    - "*.py"
    - "*.js"
    - "*.ts"
    - "*.md"
    - "*.yaml"
    - "*.json"
  ignore_patterns:
    - "__pycache__"
    - ".git"
    - "node_modules"
    - "*.pyc"
    - ".DS_Store"

# 记忆管理配置
memory:
  max_entries: 10000  # 最大记忆条目数
  cleanup_interval: 86400  # 清理间隔（秒）
  retention_days: 30  # 记忆保留天数
  auto_tag: true  # 自动标签生成
  deduplication: true  # 启用去重

# API配置
api:
  host: "localhost"
  port: 8080
  enable_cors: true
  max_request_size: 10485760  # 10MB

# 日志配置
logging:
  level: "INFO"
  file: "logs/automem.log"
  max_size: 10485760  # 10MB
  backup_count: 3

# 上下文收集配置
context:
  collect_code_structure: true
  collect_file_changes: true
  collect_conversations: true
  collect_tasks: true
  max_content_length: 5000  # 单个记忆内容最大长度
